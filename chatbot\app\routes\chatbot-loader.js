// This route serves the chatbot loader script dynamically
export async function loader({ request }) {
  const url = new URL(request.url);
  const appUrl = process.env.SHOPIFY_APP_URL || url.origin;

  // Generate the chatbot loader script with dynamic configuration
  const script = `
(function () {
  // Prevent multiple initializations
  if (window.ChatbotLoaderInitialized) {
    return;
  }
  window.ChatbotLoaderInitialized = true;

  // Configuration
  const config = {
    // Replace with your actual chatbot embed script URL
    embedScriptUrl: "https://united-become-allowance-hide.trycloudflare.com/chatbot-embed.js",
    // App URL for API calls if needed
    appUrl: "${appUrl}",
    // Timeout for script loading (in milliseconds)
    loadTimeout: 10000,
    // Retry attempts if script fails to load
    maxRetries: 3
  };

  let retryCount = 0;

  function loadChatbotScript() {
    const script = document.createElement("script");
    script.src = config.embedScriptUrl;
    script.async = true;
    script.defer = true;

    // Set up timeout
    const timeoutId = setTimeout(() => {
      console.warn("Chatbot script loading timed out");
      handleScriptError();
    }, config.loadTimeout);

    script.onload = function () {
      clearTimeout(timeoutId);
      initializeChatbot();
    };

    script.onerror = function () {
      clearTimeout(timeoutId);
      console.error("Failed to load chatbot script");
      handleScriptError();
    };

    document.head.appendChild(script);
  }

  function handleScriptError() {
    retryCount++;
    if (retryCount < config.maxRetries) {
      console.log(\`Retrying chatbot script load (attempt \${retryCount + 1}/\${config.maxRetries})\`);
      setTimeout(loadChatbotScript, 2000 * retryCount); // Exponential backoff
    } else {
      console.error("Failed to load chatbot script after maximum retries");
    }
  }

  function initializeChatbot() {
    try {
      // Get customer information from Shopify
      const customerId = window.Shopify?.customer?.id || null;
      const customerEmail = window.Shopify?.customer?.email || null;
      const customerFirstName = window.Shopify?.customer?.first_name || null;
      const customerLastName = window.Shopify?.customer?.last_name || null;

      // Get shop information
      const shopDomain = window.Shopify?.shop || window.location.hostname;

      // Prepare chatbot configuration
      const chatbotConfig = {
        customerId: customerId,
        customer: {
          id: customerId,
          email: customerEmail,
          firstName: customerFirstName,
          lastName: customerLastName,
          isLoggedIn: !!customerId
        },
        shop: {
          domain: shopDomain
        },
        // Add current page context
        page: {
          url: window.location.href,
          path: window.location.pathname,
          title: document.title
        },
        // App configuration
        app: {
          url: config.appUrl
        }
      };

      // Initialize chatbot if available
      if (window.Chatbot && typeof window.Chatbot.init === "function") {
        window.Chatbot.init(chatbotConfig);
        console.log("Chatbot initialized successfully", chatbotConfig);
      } else {
        console.warn("Chatbot object not found or init method not available");
      }
    } catch (error) {
      console.error("Error initializing chatbot:", error);
    }
  }

  // Start loading the chatbot script
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", loadChatbotScript);
  } else {
    loadChatbotScript();
  }
})();
  `.trim();

  return new Response(script, {
    headers: {
      "Content-Type": "application/javascript",
      "Cache-Control": "public, max-age=3600", // Cache for 1 hour
    },
  });
}