window.__remixManifest={"entry":{"module":"/assets/entry.client-C6-_jNcr.js","imports":["/assets/index-C0U6NBub.js","/assets/components-DH2eW-1_.js"],"css":[]},"routes":{"root":{"id":"root","path":"","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/root-BIPHRFcV.js","imports":["/assets/index-C0U6NBub.js","/assets/components-DH2eW-1_.js"],"css":[]},"routes/webhooks.app.scopes_update":{"id":"routes/webhooks.app.scopes_update","parentId":"root","path":"webhooks/app/scopes_update","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/webhooks.app.scopes_update-l0sNRNKZ.js","imports":[],"css":[]},"routes/webhooks.app.uninstalled":{"id":"routes/webhooks.app.uninstalled","parentId":"root","path":"webhooks/app/uninstalled","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/webhooks.app.uninstalled-l0sNRNKZ.js","imports":[],"css":[]},"routes/webhooks.app.installed":{"id":"routes/webhooks.app.installed","parentId":"root","path":"webhooks/app/installed","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/webhooks.app.installed-l0sNRNKZ.js","imports":[],"css":[]},"routes/chatbot-loader":{"id":"routes/chatbot-loader","parentId":"root","path":"chatbot-loader","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/chatbot-loader-l0sNRNKZ.js","imports":[],"css":[]},"routes/auth.login":{"id":"routes/auth.login","parentId":"root","path":"auth/login","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/route-CYGPIop-.js","imports":["/assets/index-C0U6NBub.js","/assets/styles-DJqeKJDA.js","/assets/components-DH2eW-1_.js","/assets/Page-BfvU96Zb.js","/assets/context-JLkqRqjJ.js"],"css":[]},"routes/callback":{"id":"routes/callback","parentId":"root","path":"callback","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/callback-l0sNRNKZ.js","imports":[],"css":[]},"routes/install":{"id":"routes/install","parentId":"root","path":"install","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/install-DZxNS3hy.js","imports":["/assets/index-C0U6NBub.js","/assets/components-DH2eW-1_.js"],"css":[]},"routes/webhook":{"id":"routes/webhook","parentId":"root","path":"webhook","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/webhook-l0sNRNKZ.js","imports":[],"css":[]},"routes/auth.$":{"id":"routes/auth.$","parentId":"root","path":"auth/*","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/auth._-l0sNRNKZ.js","imports":[],"css":[]},"routes/_index":{"id":"routes/_index","parentId":"root","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/route-BMrpPe0Q.js","imports":["/assets/index-C0U6NBub.js","/assets/components-DH2eW-1_.js"],"css":["/assets/route-Cnm7FvdT.css"]},"routes/app":{"id":"routes/app","parentId":"root","path":"app","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":true,"module":"/assets/app-Dzw3XLy2.js","imports":["/assets/index-C0U6NBub.js","/assets/components-DH2eW-1_.js","/assets/styles-DJqeKJDA.js","/assets/context-JLkqRqjJ.js"],"css":[]},"routes/app.additional":{"id":"routes/app.additional","parentId":"routes/app","path":"additional","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app.additional-DmlQSF_t.js","imports":["/assets/index-C0U6NBub.js","/assets/Page-BfvU96Zb.js","/assets/TitleBar-DgfPhC9i.js","/assets/context-JLkqRqjJ.js"],"css":[]},"routes/app._index":{"id":"routes/app._index","parentId":"routes/app","index":true,"hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app._index-CP0_eIeI.js","imports":["/assets/index-C0U6NBub.js","/assets/components-DH2eW-1_.js","/assets/Page-BfvU96Zb.js","/assets/TitleBar-DgfPhC9i.js","/assets/context-JLkqRqjJ.js"],"css":[]}},"url":"/assets/manifest-8942489e.js","version":"8942489e"};