import { authenticate } from "../shopify.server";

/**
 * Creates a script tag in the Shopify store using GraphQL
 * @param {Object} session - Shopify session
 * @param {string} src - Script source URL
 * @param {string} displayScope - Where to display the script (ONLINE_STORE, ORDER_STATUS, ALL)
 * @returns {Promise<Object>} Created script tag
 */
export async function createScriptTag(session, src, displayScope = "ONLINE_STORE") {
  const { admin } = await authenticate.admin({ session });

  const client = new admin.graphql.client({ session });

  const mutation = `
    mutation ScriptTagCreate($input: ScriptTagInput!) {
      scriptTagCreate(input: $input) {
        scriptTag {
          id
          cache
          createdAt
          displayScope
          src
          updatedAt
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    input: {
      src: src,
      displayScope: displayScope,
      cache: true
    }
  };

  const response = await client.query({
    data: { query: mutation, variables }
  });

  if (response.body?.data?.scriptTagCreate?.userErrors?.length > 0) {
    throw new Error(`Script tag creation failed: ${response.body.data.scriptTagCreate.userErrors.map(e => e.message).join(', ')}`);
  }

  return response.body?.data?.scriptTagCreate?.scriptTag;
}

/**
 * Gets all script tags for the store
 * @param {Object} session - Shopify session
 * @returns {Promise<Array>} Array of script tags
 */
export async function getScriptTags(session) {
  const { admin } = await authenticate.admin({ session });
  
  const scriptTags = await admin.rest.resources.ScriptTag.all({
    session,
  });
  
  return scriptTags.data;
}

/**
 * Finds script tags by source URL
 * @param {Object} session - Shopify session
 * @param {string} src - Script source URL to search for
 * @returns {Promise<Array>} Array of matching script tags
 */
export async function findScriptTagsBySrc(session, src) {
  const scriptTags = await getScriptTags(session);
  return scriptTags.filter(tag => tag.src === src);
}

/**
 * Deletes a script tag by ID
 * @param {Object} session - Shopify session
 * @param {string|number} scriptTagId - Script tag ID to delete
 * @returns {Promise<void>}
 */
export async function deleteScriptTag(session, scriptTagId) {
  const { admin } = await authenticate.admin({ session });
  
  await admin.rest.resources.ScriptTag.delete({
    session,
    id: scriptTagId,
  });
}

/**
 * Removes all script tags with a specific source URL
 * @param {Object} session - Shopify session
 * @param {string} src - Script source URL to remove
 * @returns {Promise<number>} Number of script tags removed
 */
export async function removeScriptTagsBySrc(session, src) {
  const scriptTags = await findScriptTagsBySrc(session, src);
  
  for (const scriptTag of scriptTags) {
    await deleteScriptTag(session, scriptTag.id);
  }
  
  return scriptTags.length;
}

/**
 * Ensures a script tag exists (creates if not found, updates if different)
 * @param {Object} session - Shopify session
 * @param {string} src - Script source URL
 * @param {string} displayScope - Where to display the script
 * @returns {Promise<Object>} Script tag (created or existing)
 */
export async function ensureScriptTag(session, src, displayScope = "online_store") {
  const existingTags = await findScriptTagsBySrc(session, src);
  
  if (existingTags.length > 0) {
    // Return the first existing tag
    return existingTags[0];
  }
  
  // Create new script tag
  return await createScriptTag(session, src, displayScope);
}

/**
 * Gets the chatbot script URL for the current app
 * @param {string} appUrl - The app's base URL
 * @returns {string} Full script URL
 */
export function getChatbotScriptUrl(appUrl) {
  // Remove trailing slash if present
  const baseUrl = appUrl.replace(/\/$/, '');
  return `${baseUrl}/chatbot-loader.js`;
}
