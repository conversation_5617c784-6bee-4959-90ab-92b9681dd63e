import { authenticate } from "../shopify.server";
import db from "../db.server";
import { removeScriptTagsBySrc, getChatbotScriptUrl } from "../utils/scriptTags.server";

export const action = async ({ request }) => {
  const { shop, session, topic } = await authenticate.webhook(request);

  console.log(`Received ${topic} webhook for ${shop}`);

  // Webhook requests can trigger multiple times and after an app has already been uninstalled.
  // If this webhook already ran, the session may have been deleted previously.
  if (session) {
    try {
      // Remove chatbot script tags before cleaning up session
      const appUrl = process.env.SHOPIFY_APP_URL;
      if (appUrl) {
        const scriptUrl = getChatbotScriptUrl(appUrl);
        const removedCount = await removeScriptTagsBySrc(session, scriptUrl);
        console.log(`Removed ${removedCount} chatbot script tags for ${shop}`);
      }
    } catch (scriptError) {
      console.error(`Failed to remove script tags for ${shop}:`, scriptError);
      // Continue with session cleanup even if script tag removal fails
    }

    // Clean up session data
    await db.session.deleteMany({ where: { shop } });
  }

  return new Response();
};
