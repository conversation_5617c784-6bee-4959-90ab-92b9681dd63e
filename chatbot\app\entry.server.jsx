const express = require("express");
const { createRequestHandler } = require("@remix-run/express");
const { install } = require("@shopify/shopify-api");
require("dotenv").config();

const app = express();

app.use(express.static("public"));
app.use("/chatbot-loader.js", (req, res) => {
  res.set("Content-Type", "application/javascript");
  res.sendFile(__dirname + "/app/routes/chatbot-loader.js");
});

app.all("*", createRequestHandler({ build: require("./build") }));

app.listen(3000, () => {
  console.log("🚀 Running on http://localhost:3000");
});