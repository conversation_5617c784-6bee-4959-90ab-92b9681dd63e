// Chatbot embed script route
export async function loader({ request }) {
  const script = `
// Chatbot Embed Script
(function() {
  'use strict';
  
  // Create chatbot namespace
  window.Chatbot = window.Chatbot || {};
  
  // Chatbot configuration and initialization
  window.Chatbot.init = function(config) {
    console.log('🤖 Chatbot initializing with config:', config);
    
    // Store configuration
    window.Chatbot.config = config;
    
    // Create chatbot UI
    createChatbotUI(config);
  };
  
  function createChatbotUI(config) {
    // Remove existing chatbot if present
    const existingChatbot = document.getElementById('shopify-chatbot');
    if (existingChatbot) {
      existingChatbot.remove();
    }
    
    // Create chatbot container
    const chatbotContainer = document.createElement('div');
    chatbotContainer.id = 'shopify-chatbot';
    chatbotContainer.innerHTML = \`
      <div id="chatbot-widget" style="
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: all 0.3s ease;
      ">
        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
          <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
        </svg>
      </div>
      
      <div id="chatbot-panel" style="
        position: fixed;
        bottom: 90px;
        right: 20px;
        width: 350px;
        height: 500px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        display: none;
        flex-direction: column;
        z-index: 9998;
        overflow: hidden;
      ">
        <div style="
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <h3 style="margin: 0; font-size: 16px;">Chat Support</h3>
          <p style="margin: 4px 0 0 0; font-size: 12px; opacity: 0.9;">
            \${config.customer.isLoggedIn ? \`Hi \${config.customer.firstName || 'there'}!\` : 'How can we help you?'}
          </p>
        </div>
        
        <div id="chat-messages" style="
          flex: 1;
          padding: 16px;
          overflow-y: auto;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <div style="
            background: #f1f3f4;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 12px;
            font-size: 14px;
          ">
            Welcome to our store! I'm here to help you find what you're looking for.
            \${config.customer.isLoggedIn ? 
              \`<br><br>I can see you're logged in as \${config.customer.email}. How can I assist you today?\` : 
              '<br><br>Feel free to ask me any questions about our products!'
            }
          </div>
        </div>
        
        <div style="
          padding: 16px;
          border-top: 1px solid #e0e0e0;
        ">
          <div style="display: flex; gap: 8px;">
            <input 
              type="text" 
              id="chat-input" 
              placeholder="Type your message..."
              style="
                flex: 1;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 20px;
                outline: none;
                font-size: 14px;
              "
            />
            <button 
              id="send-button"
              style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    \`;
    
    document.body.appendChild(chatbotContainer);
    
    // Add event listeners
    setupEventListeners(config);
  }
  
  function setupEventListeners(config) {
    const widget = document.getElementById('chatbot-widget');
    const panel = document.getElementById('chatbot-panel');
    const input = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-button');
    
    // Toggle chat panel
    widget.addEventListener('click', () => {
      const isVisible = panel.style.display === 'flex';
      panel.style.display = isVisible ? 'none' : 'flex';
      
      if (!isVisible) {
        input.focus();
      }
    });
    
    // Send message functionality
    function sendMessage() {
      const message = input.value.trim();
      if (!message) return;
      
      addMessage(message, 'user');
      input.value = '';
      
      // Simulate bot response
      setTimeout(() => {
        const responses = [
          "Thanks for your message! I'm a demo chatbot.",
          "I can help you with product information and support.",
          "Feel free to browse our products while we chat!",
          \`I can see you're on the page: \${config.page.title}\`,
          config.customer.isLoggedIn ? 
            \`Hi \${config.customer.firstName}, how can I help you today?\` : 
            "Would you like to create an account for a better experience?"
        ];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        addMessage(randomResponse, 'bot');
      }, 1000);
    }
    
    sendButton.addEventListener('click', sendMessage);
    input.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessage();
      }
    });
  }
  
  function addMessage(text, sender) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    
    messageDiv.style.cssText = \`
      margin-bottom: 12px;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 14px;
      max-width: 80%;
      \${sender === 'user' ? 
        'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-left: auto; text-align: right;' : 
        'background: #f1f3f4; color: #333;'
      }
    \`;
    
    messageDiv.textContent = text;
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }
  
  console.log('🤖 Chatbot embed script loaded successfully');
})();
  `.trim();

  return new Response(script, {
    headers: {
      "Content-Type": "application/javascript",
      "Cache-Control": "public, max-age=3600",
    },
  });
}
