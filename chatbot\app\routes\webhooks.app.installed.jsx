import { authenticate } from "../shopify.server";
import { ensureScriptTag, getChatbotScriptUrl } from "../utils/scriptTags.server";

export const action = async ({ request }) => {
  try {
    const { shop, session, topic } = await authenticate.webhook(request);

    console.log(`Received ${topic} webhook for ${shop}`);

    if (!session) {
      console.error("No session found for app installation webhook");
      return new Response("No session found", { status: 400 });
    }

    // Get the app URL from environment variables
    const appUrl = process.env.SHOPIFY_APP_URL;
    if (!appUrl) {
      console.error("SHOPIFY_APP_URL environment variable not set");
      return new Response("App URL not configured", { status: 500 });
    }

    // Create the script tag URL
    const scriptUrl = getChatbotScriptUrl(appUrl);

    try {
      // Ensure the chatbot script tag is installed
      const scriptTag = await ensureScriptTag(session, scriptUrl, "online_store");
      
      console.log(`Script tag created/verified for ${shop}:`, {
        id: scriptTag.id,
        src: scriptTag.src,
        display_scope: scriptTag.display_scope
      });

    } catch (scriptError) {
      console.error(`Failed to create script tag for ${shop}:`, scriptError);
      // Don't fail the webhook, just log the error
    }

    return new Response("OK", { status: 200 });

  } catch (error) {
    console.error("Error processing app installation webhook:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
};
