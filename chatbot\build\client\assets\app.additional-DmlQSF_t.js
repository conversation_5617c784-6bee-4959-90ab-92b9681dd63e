import{j as e}from"./index-C0U6NBub.js";import{P as p,C as s,B as r,T as a,a as l}from"./Page-BfvU96Zb.js";import{T as c,L as i,a as d,b as t}from"./TitleBar-DgfPhC9i.js";import"./context-JLkqRqjJ.js";function u(){return e.jsxs(p,{children:[e.jsx(c,{title:"Additional page"}),e.jsxs(i,{children:[e.jsx(i.Section,{children:e.jsx(s,{children:e.jsxs(r,{gap:"300",children:[e.jsxs(a,{as:"p",variant:"bodyMd",children:["The app template comes with an additional page which demonstrates how to create multiple pages within app navigation using"," ",e.jsx(d,{url:"https://shopify.dev/docs/apps/tools/app-bridge",target:"_blank",removeUnderline:!0,children:"App Bridge"}),"."]}),e.jsxs(a,{as:"p",variant:"bodyMd",children:["To create your own page and have it show up in the app navigation, add a page inside ",e.jsx(n,{children:"app/routes"}),", and a link to it in the ",e.jsx(n,{children:"<NavMenu>"})," component found in ",e.jsx(n,{children:"app/routes/app.jsx"}),"."]})]})})}),e.jsx(i.Section,{variant:"oneThird",children:e.jsx(s,{children:e.jsxs(r,{gap:"200",children:[e.jsx(a,{as:"h2",variant:"headingMd",children:"Resources"}),e.jsx(t,{children:e.jsx(t.Item,{children:e.jsx(d,{url:"https://shopify.dev/docs/apps/design-guidelines/navigation#app-nav",target:"_blank",removeUnderline:!0,children:"App nav best practices"})})})]})})})]})]})}function n({children:o}){return e.jsx(l,{as:"span",padding:"025",paddingInlineStart:"100",paddingInlineEnd:"100",background:"bg-surface-active",borderWidth:"025",borderColor:"border",borderRadius:"100",children:e.jsx("code",{children:o})})}export{u as default};
