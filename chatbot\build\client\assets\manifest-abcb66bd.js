window.__remixManifest={"entry":{"module":"/assets/entry.client-B4p3FXZU.js","imports":["/assets/index-C0U6NBub.js","/assets/components-qeoBKuxV.js"],"css":[]},"routes":{"root":{"id":"root","path":"","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/root-BZn3ATJ0.js","imports":["/assets/index-C0U6NBub.js","/assets/components-qeoBKuxV.js"],"css":[]},"routes/webhooks.app.scopes_update":{"id":"routes/webhooks.app.scopes_update","parentId":"root","path":"webhooks/app/scopes_update","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/webhooks.app.scopes_update-l0sNRNKZ.js","imports":[],"css":[]},"routes/webhooks.app.uninstalled":{"id":"routes/webhooks.app.uninstalled","parentId":"root","path":"webhooks/app/uninstalled","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/webhooks.app.uninstalled-l0sNRNKZ.js","imports":[],"css":[]},"routes/chatbot-loader":{"id":"routes/chatbot-loader","parentId":"root","path":"chatbot-loader","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/chatbot-loader-l0sNRNKZ.js","imports":[],"css":[]},"routes/chatbot-embed":{"id":"routes/chatbot-embed","parentId":"root","path":"chatbot-embed","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/chatbot-embed-l0sNRNKZ.js","imports":[],"css":[]},"routes/auth.login":{"id":"routes/auth.login","parentId":"root","path":"auth/login","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/route-CNoc41jw.js","imports":["/assets/index-C0U6NBub.js","/assets/styles-bRXb-Ils.js","/assets/components-qeoBKuxV.js","/assets/Page-BAvRnlvb.js","/assets/context-BJzNQyky.js"],"css":[]},"routes/callback":{"id":"routes/callback","parentId":"root","path":"callback","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/callback-l0sNRNKZ.js","imports":[],"css":[]},"routes/install":{"id":"routes/install","parentId":"root","path":"install","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/install-afEcw40f.js","imports":["/assets/index-C0U6NBub.js","/assets/components-qeoBKuxV.js"],"css":[]},"routes/webhook":{"id":"routes/webhook","parentId":"root","path":"webhook","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/webhook-l0sNRNKZ.js","imports":[],"css":[]},"routes/auth.$":{"id":"routes/auth.$","parentId":"root","path":"auth/*","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/auth._-l0sNRNKZ.js","imports":[],"css":[]},"routes/_index":{"id":"routes/_index","parentId":"root","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/route-BDQf8MaJ.js","imports":["/assets/index-C0U6NBub.js","/assets/components-qeoBKuxV.js"],"css":["/assets/route-Cnm7FvdT.css"]},"routes/app":{"id":"routes/app","parentId":"root","path":"app","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":true,"module":"/assets/app-BnasjhWB.js","imports":["/assets/index-C0U6NBub.js","/assets/components-qeoBKuxV.js","/assets/styles-bRXb-Ils.js","/assets/context-BJzNQyky.js"],"css":[]},"routes/app.additional":{"id":"routes/app.additional","parentId":"routes/app","path":"additional","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app.additional-CzDoWRGl.js","imports":["/assets/index-C0U6NBub.js","/assets/Page-BAvRnlvb.js","/assets/TitleBar-DiPv-53d.js","/assets/context-BJzNQyky.js"],"css":[]},"routes/app._index":{"id":"routes/app._index","parentId":"routes/app","index":true,"hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app._index-CIUjNsmD.js","imports":["/assets/index-C0U6NBub.js","/assets/components-qeoBKuxV.js","/assets/Page-BAvRnlvb.js","/assets/TitleBar-DiPv-53d.js","/assets/context-BJzNQyky.js"],"css":[]}},"url":"/assets/manifest-abcb66bd.js","version":"abcb66bd"};