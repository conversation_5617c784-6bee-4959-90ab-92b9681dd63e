var Sa=Object.defineProperty;var Ra=(e,t,r)=>t in e?Sa(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var St=(e,t,r)=>Ra(e,typeof t!="symbol"?t+"":t,r);import{r as d,b as mn,c as xa}from"./index-C0U6NBub.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Y(){return Y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Y.apply(this,arguments)}var Z;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Z||(Z={}));const Ur="popstate";function $l(e){e===void 0&&(e={});function t(n,a){let{pathname:i,search:o,hash:s}=n.location;return lt("",{pathname:i,search:o,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:_e(a)}return Pa(t,r,null,e)}function k(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ke(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function La(){return Math.random().toString(36).substr(2,8)}function $r(e,t){return{usr:e.state,key:e.key,idx:t}}function lt(e,t,r,n){return r===void 0&&(r=null),Y({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Se(t):t,{state:r,key:t&&t.key||n||La()})}function _e(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function Se(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function Pa(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:i=!1}=n,o=a.history,s=Z.Pop,l=null,u=h();u==null&&(u=0,o.replaceState(Y({},o.state,{idx:u}),""));function h(){return(o.state||{idx:null}).idx}function c(){s=Z.Pop;let b=h(),D=b==null?null:b-u;u=b,l&&l({action:s,location:E.location,delta:D})}function m(b,D){s=Z.Push;let R=lt(E.location,b,D);u=h()+1;let P=$r(R,u),x=E.createHref(R);try{o.pushState(P,"",x)}catch(A){if(A instanceof DOMException&&A.name==="DataCloneError")throw A;a.location.assign(x)}i&&l&&l({action:s,location:E.location,delta:1})}function g(b,D){s=Z.Replace;let R=lt(E.location,b,D);u=h();let P=$r(R,u),x=E.createHref(R);o.replaceState(P,"",x),i&&l&&l({action:s,location:E.location,delta:0})}function v(b){let D=a.location.origin!=="null"?a.location.origin:a.location.href,R=typeof b=="string"?b:_e(b);return R=R.replace(/ $/,"%20"),k(D,"No window.location.(origin|href) available to create URL for href: "+R),new URL(R,D)}let E={get action(){return s},get location(){return e(a,o)},listen(b){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(Ur,c),l=b,()=>{a.removeEventListener(Ur,c),l=null}},createHref(b){return t(a,b)},createURL:v,encodeLocation(b){let D=v(b);return{pathname:D.pathname,search:D.search,hash:D.hash}},push:m,replace:g,go(b){return o.go(b)}};return E}var J;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(J||(J={}));const Da=new Set(["lazy","caseSensitive","path","id","index","children"]);function Ca(e){return e.index===!0}function Ft(e,t,r,n){return r===void 0&&(r=[]),n===void 0&&(n={}),e.map((a,i)=>{let o=[...r,String(i)],s=typeof a.id=="string"?a.id:o.join("-");if(k(a.index!==!0||!a.children,"Cannot specify children on an index route"),k(!n[s],'Found a route id collision on id "'+s+`".  Route id's must be globally unique within Data Router usages`),Ca(a)){let l=Y({},a,t(a),{id:s});return n[s]=l,l}else{let l=Y({},a,t(a),{id:s,children:void 0});return n[s]=l,a.children&&(l.children=Ft(a.children,t,o,n)),l}})}function Ee(e,t,r){return r===void 0&&(r="/"),_t(e,t,r,!1)}function _t(e,t,r,n){let a=typeof t=="string"?Se(t):t,i=ue(a.pathname||"/",r);if(i==null)return null;let o=vn(e);_a(o);let s=null;for(let l=0;s==null&&l<o.length;++l){let u=$a(i);s=Ia(o[l],u,n)}return s}function pn(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function vn(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(i,o,s)=>{let l={relativePath:s===void 0?i.path||"":s,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};l.relativePath.startsWith("/")&&(k(l.relativePath.startsWith(n),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(n.length));let u=me([n,l.relativePath]),h=r.concat(l);i.children&&i.children.length>0&&(k(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),vn(i.children,t,h,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:ja(u,i.index),routesMeta:h})};return e.forEach((i,o)=>{var s;if(i.path===""||!((s=i.path)!=null&&s.includes("?")))a(i,o);else for(let l of yn(i.path))a(i,o,l)}),t}function yn(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),i=r.replace(/\?$/,"");if(n.length===0)return a?[i,""]:[i];let o=yn(n.join("/")),s=[];return s.push(...o.map(l=>l===""?i:[i,l].join("/"))),a&&s.push(...o),s.map(l=>e.startsWith("/")&&l===""?"/":l)}function _a(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:ka(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Ta=/^:[\w-]+$/,Oa=3,Ma=2,Fa=1,Aa=10,Na=-2,Hr=e=>e==="*";function ja(e,t){let r=e.split("/"),n=r.length;return r.some(Hr)&&(n+=Na),t&&(n+=Ma),r.filter(a=>!Hr(a)).reduce((a,i)=>a+(Ta.test(i)?Oa:i===""?Fa:Aa),n)}function ka(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Ia(e,t,r){r===void 0&&(r=!1);let{routesMeta:n}=e,a={},i="/",o=[];for(let s=0;s<n.length;++s){let l=n[s],u=s===n.length-1,h=i==="/"?t:t.slice(i.length)||"/",c=At({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},h),m=l.route;if(!c&&u&&r&&!n[n.length-1].route.index&&(c=At({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},h)),!c)return null;Object.assign(a,c.params),o.push({params:a,pathname:me([i,c.pathname]),pathnameBase:za(me([i,c.pathnameBase])),route:m}),c.pathnameBase!=="/"&&(i=me([i,c.pathnameBase]))}return o}function At(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Ua(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let i=a[0],o=i.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:n.reduce((u,h,c)=>{let{paramName:m,isOptional:g}=h;if(m==="*"){let E=s[c]||"";o=i.slice(0,i.length-E.length).replace(/(.)\/+$/,"$1")}const v=s[c];return g&&!v?u[m]=void 0:u[m]=(v||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:o,pattern:e}}function Ua(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Ke(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,s,l)=>(n.push({paramName:s,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function $a(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ke(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ue(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Ha(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?Se(e):e;return{pathname:r?r.startsWith("/")?r:Ba(r,t):t,search:Wa(n),hash:Ja(a)}}function Ba(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Xt(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function gn(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function ur(e,t){let r=gn(e);return t?r.map((n,a)=>a===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function cr(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=Se(e):(a=Y({},e),k(!a.pathname||!a.pathname.includes("?"),Xt("?","pathname","search",a)),k(!a.pathname||!a.pathname.includes("#"),Xt("#","pathname","hash",a)),k(!a.search||!a.search.includes("#"),Xt("#","search","hash",a)));let i=e===""||a.pathname==="",o=i?"/":a.pathname,s;if(o==null)s=r;else{let c=t.length-1;if(!n&&o.startsWith("..")){let m=o.split("/");for(;m[0]==="..";)m.shift(),c-=1;a.pathname=m.join("/")}s=c>=0?t[c]:"/"}let l=Ha(a,s),u=o&&o!=="/"&&o.endsWith("/"),h=(i||o===".")&&r.endsWith("/");return!l.pathname.endsWith("/")&&(u||h)&&(l.pathname+="/"),l}const me=e=>e.join("/").replace(/\/\/+/g,"/"),za=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Wa=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ja=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class Ka{constructor(t,r){this.type="DataWithResponseInit",this.data=t,this.init=r||null}}function Va(e,t){return new Ka(e,typeof t=="number"?{status:t}:t)}class Nt extends Error{}class Ya{constructor(t,r){this.pendingKeysSet=new Set,this.subscribers=new Set,this.deferredKeys=[],k(t&&typeof t=="object"&&!Array.isArray(t),"defer() only accepts plain objects");let n;this.abortPromise=new Promise((i,o)=>n=o),this.controller=new AbortController;let a=()=>n(new Nt("Deferred data aborted"));this.unlistenAbortSignal=()=>this.controller.signal.removeEventListener("abort",a),this.controller.signal.addEventListener("abort",a),this.data=Object.entries(t).reduce((i,o)=>{let[s,l]=o;return Object.assign(i,{[s]:this.trackPromise(s,l)})},{}),this.done&&this.unlistenAbortSignal(),this.init=r}trackPromise(t,r){if(!(r instanceof Promise))return r;this.deferredKeys.push(t),this.pendingKeysSet.add(t);let n=Promise.race([r,this.abortPromise]).then(a=>this.onSettle(n,t,void 0,a),a=>this.onSettle(n,t,a));return n.catch(()=>{}),Object.defineProperty(n,"_tracked",{get:()=>!0}),n}onSettle(t,r,n,a){if(this.controller.signal.aborted&&n instanceof Nt)return this.unlistenAbortSignal(),Object.defineProperty(t,"_error",{get:()=>n}),Promise.reject(n);if(this.pendingKeysSet.delete(r),this.done&&this.unlistenAbortSignal(),n===void 0&&a===void 0){let i=new Error('Deferred data for key "'+r+'" resolved/rejected with `undefined`, you must resolve/reject with a value or `null`.');return Object.defineProperty(t,"_error",{get:()=>i}),this.emit(!1,r),Promise.reject(i)}return a===void 0?(Object.defineProperty(t,"_error",{get:()=>n}),this.emit(!1,r),Promise.reject(n)):(Object.defineProperty(t,"_data",{get:()=>a}),this.emit(!1,r),a)}emit(t,r){this.subscribers.forEach(n=>n(t,r))}subscribe(t){return this.subscribers.add(t),()=>this.subscribers.delete(t)}cancel(){this.controller.abort(),this.pendingKeysSet.forEach((t,r)=>this.pendingKeysSet.delete(r)),this.emit(!0)}async resolveData(t){let r=!1;if(!this.done){let n=()=>this.cancel();t.addEventListener("abort",n),r=await new Promise(a=>{this.subscribe(i=>{t.removeEventListener("abort",n),(i||this.done)&&a(i)})})}return r}get done(){return this.pendingKeysSet.size===0}get unwrappedData(){return k(this.data!==null&&this.done,"Can only unwrap data on initialized and settled deferreds"),Object.entries(this.data).reduce((t,r)=>{let[n,a]=r;return Object.assign(t,{[n]:Ga(a)})},{})}get pendingKeys(){return Array.from(this.pendingKeysSet)}}function Xa(e){return e instanceof Promise&&e._tracked===!0}function Ga(e){if(!Xa(e))return e;if(e._error)throw e._error;return e._data}const wn=function(t,r){r===void 0&&(r=302);let n=r;typeof n=="number"?n={status:n}:typeof n.status>"u"&&(n.status=302);let a=new Headers(n.headers);return a.set("Location",t),new Response(null,Y({},n,{headers:a}))};class ke{constructor(t,r,n,a){a===void 0&&(a=!1),this.status=t,this.statusText=r||"",this.internal=a,n instanceof Error?(this.data=n.toString(),this.error=n):this.data=n}}function Ie(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const En=["post","put","patch","delete"],Qa=new Set(En),Za=["get",...En],qa=new Set(Za),ei=new Set([301,302,303,307,308]),ti=new Set([307,308]),Gt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},bn={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},rt={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},dr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ri=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Sn="remix-router-transitions";function Hl(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u",n=!r;k(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let a;if(e.mapRouteProperties)a=e.mapRouteProperties;else if(e.detectErrorBoundary){let f=e.detectErrorBoundary;a=p=>({hasErrorBoundary:f(p)})}else a=ri;let i={},o=Ft(e.routes,a,void 0,i),s,l=e.basename||"/",u=e.dataStrategy||oi,h=e.patchRoutesOnNavigation,c=Y({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),m=null,g=new Set,v=null,E=null,b=null,D=e.hydrationData!=null,R=Ee(o,e.history.location,l),P=!1,x=null;if(R==null&&!h){let f=oe(404,{pathname:e.history.location.pathname}),{matches:p,route:w}=Zr(o);R=p,x={[w.id]:f}}R&&!e.hydrationData&&gt(R,o,e.history.location.pathname).active&&(R=null);let A;if(R)if(R.some(f=>f.route.lazy))A=!1;else if(!R.some(f=>f.route.loader))A=!0;else if(c.v7_partialHydration){let f=e.hydrationData?e.hydrationData.loaderData:null,p=e.hydrationData?e.hydrationData.errors:null;if(p){let w=R.findIndex(S=>p[S.route.id]!==void 0);A=R.slice(0,w+1).every(S=>!ar(S.route,f,p))}else A=R.every(w=>!ar(w.route,f,p))}else A=e.hydrationData!=null;else if(A=!1,R=[],c.v7_partialHydration){let f=gt(null,o,e.history.location.pathname);f.active&&f.matches&&(P=!0,R=f.matches)}let _,y={historyAction:e.history.action,location:e.history.location,matches:R,initialized:A,navigation:Gt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||x,fetchers:new Map,blockers:new Map},C=Z.Pop,$=!1,I,K=!1,W=new Map,ae=null,ne=!1,ee=!1,xe=[],ht=new Set,te=new Map,mt=0,Ge=-1,Ue=new Map,ye=new Set,$e=new Map,Qe=new Map,ce=new Set,Te=new Map,Oe=new Map,pt;function ia(){if(m=e.history.listen(f=>{let{action:p,location:w,delta:S}=f;if(pt){pt(),pt=void 0;return}Ke(Oe.size===0||S!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let L=Nr({currentLocation:y.location,nextLocation:w,historyAction:p});if(L&&S!=null){let N=new Promise(j=>{pt=j});e.history.go(S*-1),yt(L,{state:"blocked",location:w,proceed(){yt(L,{state:"proceeding",proceed:void 0,reset:void 0,location:w}),N.then(()=>e.history.go(S))},reset(){let j=new Map(y.blockers);j.set(L,rt),ie({blockers:j})}});return}return Me(p,w)}),r){Ei(t,W);let f=()=>bi(t,W);t.addEventListener("pagehide",f),ae=()=>t.removeEventListener("pagehide",f)}return y.initialized||Me(Z.Pop,y.location,{initialHydration:!0}),_}function oa(){m&&m(),ae&&ae(),g.clear(),I&&I.abort(),y.fetchers.forEach((f,p)=>vt(p)),y.blockers.forEach((f,p)=>Ar(p))}function la(f){return g.add(f),()=>g.delete(f)}function ie(f,p){p===void 0&&(p={}),y=Y({},y,f);let w=[],S=[];c.v7_fetcherPersist&&y.fetchers.forEach((L,N)=>{L.state==="idle"&&(ce.has(N)?S.push(N):w.push(N))}),ce.forEach(L=>{!y.fetchers.has(L)&&!te.has(L)&&S.push(L)}),[...g].forEach(L=>L(y,{deletedFetchers:S,viewTransitionOpts:p.viewTransitionOpts,flushSync:p.flushSync===!0})),c.v7_fetcherPersist?(w.forEach(L=>y.fetchers.delete(L)),S.forEach(L=>vt(L))):S.forEach(L=>ce.delete(L))}function He(f,p,w){var S,L;let{flushSync:N}=w===void 0?{}:w,j=y.actionData!=null&&y.navigation.formMethod!=null&&de(y.navigation.formMethod)&&y.navigation.state==="loading"&&((S=f.state)==null?void 0:S._isRedirect)!==!0,O;p.actionData?Object.keys(p.actionData).length>0?O=p.actionData:O=null:j?O=y.actionData:O=null;let M=p.loaderData?Gr(y.loaderData,p.loaderData,p.matches||[],p.errors):y.loaderData,T=y.blockers;T.size>0&&(T=new Map(T),T.forEach((H,re)=>T.set(re,rt)));let F=$===!0||y.navigation.formMethod!=null&&de(y.navigation.formMethod)&&((L=f.state)==null?void 0:L._isRedirect)!==!0;s&&(o=s,s=void 0),ne||C===Z.Pop||(C===Z.Push?e.history.push(f,f.state):C===Z.Replace&&e.history.replace(f,f.state));let U;if(C===Z.Pop){let H=W.get(y.location.pathname);H&&H.has(f.pathname)?U={currentLocation:y.location,nextLocation:f}:W.has(f.pathname)&&(U={currentLocation:f,nextLocation:y.location})}else if(K){let H=W.get(y.location.pathname);H?H.add(f.pathname):(H=new Set([f.pathname]),W.set(y.location.pathname,H)),U={currentLocation:y.location,nextLocation:f}}ie(Y({},p,{actionData:O,loaderData:M,historyAction:C,location:f,initialized:!0,navigation:Gt,revalidation:"idle",restoreScrollPosition:kr(f,p.matches||y.matches),preventScrollReset:F,blockers:T}),{viewTransitionOpts:U,flushSync:N===!0}),C=Z.Pop,$=!1,K=!1,ne=!1,ee=!1,xe=[]}async function Dr(f,p){if(typeof f=="number"){e.history.go(f);return}let w=nr(y.location,y.matches,l,c.v7_prependBasename,f,c.v7_relativeSplatPath,p==null?void 0:p.fromRouteId,p==null?void 0:p.relative),{path:S,submission:L,error:N}=Br(c.v7_normalizeFormMethod,!1,w,p),j=y.location,O=lt(y.location,S,p&&p.state);O=Y({},O,e.history.encodeLocation(O));let M=p&&p.replace!=null?p.replace:void 0,T=Z.Push;M===!0?T=Z.Replace:M===!1||L!=null&&de(L.formMethod)&&L.formAction===y.location.pathname+y.location.search&&(T=Z.Replace);let F=p&&"preventScrollReset"in p?p.preventScrollReset===!0:void 0,U=(p&&p.flushSync)===!0,H=Nr({currentLocation:j,nextLocation:O,historyAction:T});if(H){yt(H,{state:"blocked",location:O,proceed(){yt(H,{state:"proceeding",proceed:void 0,reset:void 0,location:O}),Dr(f,p)},reset(){let re=new Map(y.blockers);re.set(H,rt),ie({blockers:re})}});return}return await Me(T,O,{submission:L,pendingError:N,preventScrollReset:F,replace:p&&p.replace,enableViewTransition:p&&p.viewTransition,flushSync:U})}function sa(){if(Jt(),ie({revalidation:"loading"}),y.navigation.state!=="submitting"){if(y.navigation.state==="idle"){Me(y.historyAction,y.location,{startUninterruptedRevalidation:!0});return}Me(C||y.historyAction,y.navigation.location,{overrideNavigation:y.navigation,enableViewTransition:K===!0})}}async function Me(f,p,w){I&&I.abort(),I=null,C=f,ne=(w&&w.startUninterruptedRevalidation)===!0,ga(y.location,y.matches),$=(w&&w.preventScrollReset)===!0,K=(w&&w.enableViewTransition)===!0;let S=s||o,L=w&&w.overrideNavigation,N=w!=null&&w.initialHydration&&y.matches&&y.matches.length>0&&!P?y.matches:Ee(S,p,l),j=(w&&w.flushSync)===!0;if(N&&y.initialized&&!ee&&fi(y.location,p)&&!(w&&w.submission&&de(w.submission.formMethod))){He(p,{matches:N},{flushSync:j});return}let O=gt(N,S,p.pathname);if(O.active&&O.matches&&(N=O.matches),!N){let{error:V,notFoundMatches:z,route:X}=Kt(p.pathname);He(p,{matches:z,loaderData:{},errors:{[X.id]:V}},{flushSync:j});return}I=new AbortController;let M=Je(e.history,p,I.signal,w&&w.submission),T;if(w&&w.pendingError)T=[Ne(N).route.id,{type:J.error,error:w.pendingError}];else if(w&&w.submission&&de(w.submission.formMethod)){let V=await ua(M,p,w.submission,N,O.active,{replace:w.replace,flushSync:j});if(V.shortCircuited)return;if(V.pendingActionResult){let[z,X]=V.pendingActionResult;if(le(X)&&Ie(X.error)&&X.error.status===404){I=null,He(p,{matches:V.matches,loaderData:{},errors:{[z]:X.error}});return}}N=V.matches||N,T=V.pendingActionResult,L=Qt(p,w.submission),j=!1,O.active=!1,M=Je(e.history,M.url,M.signal)}let{shortCircuited:F,matches:U,loaderData:H,errors:re}=await ca(M,p,N,O.active,L,w&&w.submission,w&&w.fetcherSubmission,w&&w.replace,w&&w.initialHydration===!0,j,T);F||(I=null,He(p,Y({matches:U||N},Qr(T),{loaderData:H,errors:re})))}async function ua(f,p,w,S,L,N){N===void 0&&(N={}),Jt();let j=gi(p,w);if(ie({navigation:j},{flushSync:N.flushSync===!0}),L){let T=await wt(S,p.pathname,f.signal);if(T.type==="aborted")return{shortCircuited:!0};if(T.type==="error"){let F=Ne(T.partialMatches).route.id;return{matches:T.partialMatches,pendingActionResult:[F,{type:J.error,error:T.error}]}}else if(T.matches)S=T.matches;else{let{notFoundMatches:F,error:U,route:H}=Kt(p.pathname);return{matches:F,pendingActionResult:[H.id,{type:J.error,error:U}]}}}let O,M=ot(S,p);if(!M.route.action&&!M.route.lazy)O={type:J.error,error:oe(405,{method:f.method,pathname:p.pathname,routeId:M.route.id})};else if(O=(await Ze("action",y,f,[M],S,null))[M.route.id],f.signal.aborted)return{shortCircuited:!0};if(je(O)){let T;return N&&N.replace!=null?T=N.replace:T=Vr(O.response.headers.get("Location"),new URL(f.url),l)===y.location.pathname+y.location.search,await Fe(f,O,!0,{submission:w,replace:T}),{shortCircuited:!0}}if(Ce(O))throw oe(400,{type:"defer-action"});if(le(O)){let T=Ne(S,M.route.id);return(N&&N.replace)!==!0&&(C=Z.Push),{matches:S,pendingActionResult:[T.route.id,O]}}return{matches:S,pendingActionResult:[M.route.id,O]}}async function ca(f,p,w,S,L,N,j,O,M,T,F){let U=L||Qt(p,N),H=N||j||en(U),re=!ne&&(!c.v7_partialHydration||!M);if(S){if(re){let G=Cr(F);ie(Y({navigation:U},G!==void 0?{actionData:G}:{}),{flushSync:T})}let B=await wt(w,p.pathname,f.signal);if(B.type==="aborted")return{shortCircuited:!0};if(B.type==="error"){let G=Ne(B.partialMatches).route.id;return{matches:B.partialMatches,loaderData:{},errors:{[G]:B.error}}}else if(B.matches)w=B.matches;else{let{error:G,notFoundMatches:ze,route:tt}=Kt(p.pathname);return{matches:ze,loaderData:{},errors:{[tt.id]:G}}}}let V=s||o,[z,X]=Wr(e.history,y,w,H,p,c.v7_partialHydration&&M===!0,c.v7_skipActionErrorRevalidation,ee,xe,ht,ce,$e,ye,V,l,F);if(Vt(B=>!(w&&w.some(G=>G.route.id===B))||z&&z.some(G=>G.route.id===B)),Ge=++mt,z.length===0&&X.length===0){let B=Mr();return He(p,Y({matches:w,loaderData:{},errors:F&&le(F[1])?{[F[0]]:F[1].error}:null},Qr(F),B?{fetchers:new Map(y.fetchers)}:{}),{flushSync:T}),{shortCircuited:!0}}if(re){let B={};if(!S){B.navigation=U;let G=Cr(F);G!==void 0&&(B.actionData=G)}X.length>0&&(B.fetchers=da(X)),ie(B,{flushSync:T})}X.forEach(B=>{Pe(B.key),B.controller&&te.set(B.key,B.controller)});let Be=()=>X.forEach(B=>Pe(B.key));I&&I.signal.addEventListener("abort",Be);let{loaderResults:qe,fetcherResults:we}=await _r(y,w,z,X,f);if(f.signal.aborted)return{shortCircuited:!0};I&&I.signal.removeEventListener("abort",Be),X.forEach(B=>te.delete(B.key));let he=Rt(qe);if(he)return await Fe(f,he.result,!0,{replace:O}),{shortCircuited:!0};if(he=Rt(we),he)return ye.add(he.key),await Fe(f,he.result,!0,{replace:O}),{shortCircuited:!0};let{loaderData:Yt,errors:et}=Xr(y,w,qe,F,X,we,Te);Te.forEach((B,G)=>{B.subscribe(ze=>{(ze||B.done)&&Te.delete(G)})}),c.v7_partialHydration&&M&&y.errors&&(et=Y({},y.errors,et));let Ae=Mr(),Et=Fr(Ge),bt=Ae||Et||X.length>0;return Y({matches:w,loaderData:Yt,errors:et},bt?{fetchers:new Map(y.fetchers)}:{})}function Cr(f){if(f&&!le(f[1]))return{[f[0]]:f[1].data};if(y.actionData)return Object.keys(y.actionData).length===0?null:y.actionData}function da(f){return f.forEach(p=>{let w=y.fetchers.get(p.key),S=nt(void 0,w?w.data:void 0);y.fetchers.set(p.key,S)}),new Map(y.fetchers)}function fa(f,p,w,S){if(n)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");Pe(f);let L=(S&&S.flushSync)===!0,N=s||o,j=nr(y.location,y.matches,l,c.v7_prependBasename,w,c.v7_relativeSplatPath,p,S==null?void 0:S.relative),O=Ee(N,j,l),M=gt(O,N,j);if(M.active&&M.matches&&(O=M.matches),!O){ge(f,p,oe(404,{pathname:j}),{flushSync:L});return}let{path:T,submission:F,error:U}=Br(c.v7_normalizeFormMethod,!0,j,S);if(U){ge(f,p,U,{flushSync:L});return}let H=ot(O,T),re=(S&&S.preventScrollReset)===!0;if(F&&de(F.formMethod)){ha(f,p,T,H,O,M.active,L,re,F);return}$e.set(f,{routeId:p,path:T}),ma(f,p,T,H,O,M.active,L,re,F)}async function ha(f,p,w,S,L,N,j,O,M){Jt(),$e.delete(f);function T(Q){if(!Q.route.action&&!Q.route.lazy){let We=oe(405,{method:M.formMethod,pathname:w,routeId:p});return ge(f,p,We,{flushSync:j}),!0}return!1}if(!N&&T(S))return;let F=y.fetchers.get(f);Le(f,wi(M,F),{flushSync:j});let U=new AbortController,H=Je(e.history,w,U.signal,M);if(N){let Q=await wt(L,new URL(H.url).pathname,H.signal,f);if(Q.type==="aborted")return;if(Q.type==="error"){ge(f,p,Q.error,{flushSync:j});return}else if(Q.matches){if(L=Q.matches,S=ot(L,w),T(S))return}else{ge(f,p,oe(404,{pathname:w}),{flushSync:j});return}}te.set(f,U);let re=mt,z=(await Ze("action",y,H,[S],L,f))[S.route.id];if(H.signal.aborted){te.get(f)===U&&te.delete(f);return}if(c.v7_fetcherPersist&&ce.has(f)){if(je(z)||le(z)){Le(f,De(void 0));return}}else{if(je(z))if(te.delete(f),Ge>re){Le(f,De(void 0));return}else return ye.add(f),Le(f,nt(M)),Fe(H,z,!1,{fetcherSubmission:M,preventScrollReset:O});if(le(z)){ge(f,p,z.error);return}}if(Ce(z))throw oe(400,{type:"defer-action"});let X=y.navigation.location||y.location,Be=Je(e.history,X,U.signal),qe=s||o,we=y.navigation.state!=="idle"?Ee(qe,y.navigation.location,l):y.matches;k(we,"Didn't find any matches after fetcher action");let he=++mt;Ue.set(f,he);let Yt=nt(M,z.data);y.fetchers.set(f,Yt);let[et,Ae]=Wr(e.history,y,we,M,X,!1,c.v7_skipActionErrorRevalidation,ee,xe,ht,ce,$e,ye,qe,l,[S.route.id,z]);Ae.filter(Q=>Q.key!==f).forEach(Q=>{let We=Q.key,Ir=y.fetchers.get(We),ba=nt(void 0,Ir?Ir.data:void 0);y.fetchers.set(We,ba),Pe(We),Q.controller&&te.set(We,Q.controller)}),ie({fetchers:new Map(y.fetchers)});let Et=()=>Ae.forEach(Q=>Pe(Q.key));U.signal.addEventListener("abort",Et);let{loaderResults:bt,fetcherResults:B}=await _r(y,we,et,Ae,Be);if(U.signal.aborted)return;U.signal.removeEventListener("abort",Et),Ue.delete(f),te.delete(f),Ae.forEach(Q=>te.delete(Q.key));let G=Rt(bt);if(G)return Fe(Be,G.result,!1,{preventScrollReset:O});if(G=Rt(B),G)return ye.add(G.key),Fe(Be,G.result,!1,{preventScrollReset:O});let{loaderData:ze,errors:tt}=Xr(y,we,bt,void 0,Ae,B,Te);if(y.fetchers.has(f)){let Q=De(z.data);y.fetchers.set(f,Q)}Fr(he),y.navigation.state==="loading"&&he>Ge?(k(C,"Expected pending action"),I&&I.abort(),He(y.navigation.location,{matches:we,loaderData:ze,errors:tt,fetchers:new Map(y.fetchers)})):(ie({errors:tt,loaderData:Gr(y.loaderData,ze,we,tt),fetchers:new Map(y.fetchers)}),ee=!1)}async function ma(f,p,w,S,L,N,j,O,M){let T=y.fetchers.get(f);Le(f,nt(M,T?T.data:void 0),{flushSync:j});let F=new AbortController,U=Je(e.history,w,F.signal);if(N){let z=await wt(L,new URL(U.url).pathname,U.signal,f);if(z.type==="aborted")return;if(z.type==="error"){ge(f,p,z.error,{flushSync:j});return}else if(z.matches)L=z.matches,S=ot(L,w);else{ge(f,p,oe(404,{pathname:w}),{flushSync:j});return}}te.set(f,F);let H=mt,V=(await Ze("loader",y,U,[S],L,f))[S.route.id];if(Ce(V)&&(V=await fr(V,U.signal,!0)||V),te.get(f)===F&&te.delete(f),!U.signal.aborted){if(ce.has(f)){Le(f,De(void 0));return}if(je(V))if(Ge>H){Le(f,De(void 0));return}else{ye.add(f),await Fe(U,V,!1,{preventScrollReset:O});return}if(le(V)){ge(f,p,V.error);return}k(!Ce(V),"Unhandled fetcher deferred data"),Le(f,De(V.data))}}async function Fe(f,p,w,S){let{submission:L,fetcherSubmission:N,preventScrollReset:j,replace:O}=S===void 0?{}:S;p.response.headers.has("X-Remix-Revalidate")&&(ee=!0);let M=p.response.headers.get("Location");k(M,"Expected a Location header on the redirect Response"),M=Vr(M,new URL(f.url),l);let T=lt(y.location,M,{_isRedirect:!0});if(r){let z=!1;if(p.response.headers.has("X-Remix-Reload-Document"))z=!0;else if(dr.test(M)){const X=e.history.createURL(M);z=X.origin!==t.location.origin||ue(X.pathname,l)==null}if(z){O?t.location.replace(M):t.location.assign(M);return}}I=null;let F=O===!0||p.response.headers.has("X-Remix-Replace")?Z.Replace:Z.Push,{formMethod:U,formAction:H,formEncType:re}=y.navigation;!L&&!N&&U&&H&&re&&(L=en(y.navigation));let V=L||N;if(ti.has(p.response.status)&&V&&de(V.formMethod))await Me(F,T,{submission:Y({},V,{formAction:M}),preventScrollReset:j||$,enableViewTransition:w?K:void 0});else{let z=Qt(T,L);await Me(F,T,{overrideNavigation:z,fetcherSubmission:N,preventScrollReset:j||$,enableViewTransition:w?K:void 0})}}async function Ze(f,p,w,S,L,N){let j,O={};try{j=await li(u,f,p,w,S,L,N,i,a)}catch(M){return S.forEach(T=>{O[T.route.id]={type:J.error,error:M}}),O}for(let[M,T]of Object.entries(j))if(hi(T)){let F=T.result;O[M]={type:J.redirect,response:ci(F,w,M,L,l,c.v7_relativeSplatPath)}}else O[M]=await ui(T);return O}async function _r(f,p,w,S,L){let N=f.matches,j=Ze("loader",f,L,w,p,null),O=Promise.all(S.map(async F=>{if(F.matches&&F.match&&F.controller){let H=(await Ze("loader",f,Je(e.history,F.path,F.controller.signal),[F.match],F.matches,F.key))[F.match.route.id];return{[F.key]:H}}else return Promise.resolve({[F.key]:{type:J.error,error:oe(404,{pathname:F.path})}})})),M=await j,T=(await O).reduce((F,U)=>Object.assign(F,U),{});return await Promise.all([vi(p,M,L.signal,N,f.loaderData),yi(p,T,S)]),{loaderResults:M,fetcherResults:T}}function Jt(){ee=!0,xe.push(...Vt()),$e.forEach((f,p)=>{te.has(p)&&ht.add(p),Pe(p)})}function Le(f,p,w){w===void 0&&(w={}),y.fetchers.set(f,p),ie({fetchers:new Map(y.fetchers)},{flushSync:(w&&w.flushSync)===!0})}function ge(f,p,w,S){S===void 0&&(S={});let L=Ne(y.matches,p);vt(f),ie({errors:{[L.route.id]:w},fetchers:new Map(y.fetchers)},{flushSync:(S&&S.flushSync)===!0})}function Tr(f){return Qe.set(f,(Qe.get(f)||0)+1),ce.has(f)&&ce.delete(f),y.fetchers.get(f)||bn}function vt(f){let p=y.fetchers.get(f);te.has(f)&&!(p&&p.state==="loading"&&Ue.has(f))&&Pe(f),$e.delete(f),Ue.delete(f),ye.delete(f),c.v7_fetcherPersist&&ce.delete(f),ht.delete(f),y.fetchers.delete(f)}function pa(f){let p=(Qe.get(f)||0)-1;p<=0?(Qe.delete(f),ce.add(f),c.v7_fetcherPersist||vt(f)):Qe.set(f,p),ie({fetchers:new Map(y.fetchers)})}function Pe(f){let p=te.get(f);p&&(p.abort(),te.delete(f))}function Or(f){for(let p of f){let w=Tr(p),S=De(w.data);y.fetchers.set(p,S)}}function Mr(){let f=[],p=!1;for(let w of ye){let S=y.fetchers.get(w);k(S,"Expected fetcher: "+w),S.state==="loading"&&(ye.delete(w),f.push(w),p=!0)}return Or(f),p}function Fr(f){let p=[];for(let[w,S]of Ue)if(S<f){let L=y.fetchers.get(w);k(L,"Expected fetcher: "+w),L.state==="loading"&&(Pe(w),Ue.delete(w),p.push(w))}return Or(p),p.length>0}function va(f,p){let w=y.blockers.get(f)||rt;return Oe.get(f)!==p&&Oe.set(f,p),w}function Ar(f){y.blockers.delete(f),Oe.delete(f)}function yt(f,p){let w=y.blockers.get(f)||rt;k(w.state==="unblocked"&&p.state==="blocked"||w.state==="blocked"&&p.state==="blocked"||w.state==="blocked"&&p.state==="proceeding"||w.state==="blocked"&&p.state==="unblocked"||w.state==="proceeding"&&p.state==="unblocked","Invalid blocker state transition: "+w.state+" -> "+p.state);let S=new Map(y.blockers);S.set(f,p),ie({blockers:S})}function Nr(f){let{currentLocation:p,nextLocation:w,historyAction:S}=f;if(Oe.size===0)return;Oe.size>1&&Ke(!1,"A router only supports one blocker at a time");let L=Array.from(Oe.entries()),[N,j]=L[L.length-1],O=y.blockers.get(N);if(!(O&&O.state==="proceeding")&&j({currentLocation:p,nextLocation:w,historyAction:S}))return N}function Kt(f){let p=oe(404,{pathname:f}),w=s||o,{matches:S,route:L}=Zr(w);return Vt(),{notFoundMatches:S,route:L,error:p}}function Vt(f){let p=[];return Te.forEach((w,S)=>{(!f||f(S))&&(w.cancel(),p.push(S),Te.delete(S))}),p}function ya(f,p,w){if(v=f,b=p,E=w||null,!D&&y.navigation===Gt){D=!0;let S=kr(y.location,y.matches);S!=null&&ie({restoreScrollPosition:S})}return()=>{v=null,b=null,E=null}}function jr(f,p){return E&&E(f,p.map(S=>pn(S,y.loaderData)))||f.key}function ga(f,p){if(v&&b){let w=jr(f,p);v[w]=b()}}function kr(f,p){if(v){let w=jr(f,p),S=v[w];if(typeof S=="number")return S}return null}function gt(f,p,w){if(h)if(f){if(Object.keys(f[0].params).length>0)return{active:!0,matches:_t(p,w,l,!0)}}else return{active:!0,matches:_t(p,w,l,!0)||[]};return{active:!1,matches:null}}async function wt(f,p,w,S){if(!h)return{type:"success",matches:f};let L=f;for(;;){let N=s==null,j=s||o,O=i;try{await h({signal:w,path:p,matches:L,fetcherKey:S,patch:(F,U)=>{w.aborted||Kr(F,U,j,O,a)}})}catch(F){return{type:"error",error:F,partialMatches:L}}finally{N&&!w.aborted&&(o=[...o])}if(w.aborted)return{type:"aborted"};let M=Ee(j,p,l);if(M)return{type:"success",matches:M};let T=_t(j,p,l,!0);if(!T||L.length===T.length&&L.every((F,U)=>F.route.id===T[U].route.id))return{type:"success",matches:null};L=T}}function wa(f){i={},s=Ft(f,a,void 0,i)}function Ea(f,p){let w=s==null;Kr(f,p,s||o,i,a),w&&(o=[...o],ie({}))}return _={get basename(){return l},get future(){return c},get state(){return y},get routes(){return o},get window(){return t},initialize:ia,subscribe:la,enableScrollRestoration:ya,navigate:Dr,fetch:fa,revalidate:sa,createHref:f=>e.history.createHref(f),encodeLocation:f=>e.history.encodeLocation(f),getFetcher:Tr,deleteFetcher:pa,dispose:oa,getBlocker:va,deleteBlocker:Ar,patchRoutes:Ea,_internalFetchControllers:te,_internalActiveDeferreds:Te,_internalSetRoutes:wa},_}function ni(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function nr(e,t,r,n,a,i,o,s){let l,u;if(o){l=[];for(let c of t)if(l.push(c),c.route.id===o){u=c;break}}else l=t,u=t[t.length-1];let h=cr(a||".",ur(l,i),ue(e.pathname,r)||e.pathname,s==="path");if(a==null&&(h.search=e.search,h.hash=e.hash),(a==null||a===""||a===".")&&u){let c=hr(h.search);if(u.route.index&&!c)h.search=h.search?h.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&c){let m=new URLSearchParams(h.search),g=m.getAll("index");m.delete("index"),g.filter(E=>E).forEach(E=>m.append("index",E));let v=m.toString();h.search=v?"?"+v:""}}return n&&r!=="/"&&(h.pathname=h.pathname==="/"?r:me([r,h.pathname])),_e(h)}function Br(e,t,r,n){if(!n||!ni(n))return{path:r};if(n.formMethod&&!pi(n.formMethod))return{path:r,error:oe(405,{method:n.formMethod})};let a=()=>({path:r,error:oe(400,{type:"invalid-body"})}),i=n.formMethod||"get",o=e?i.toUpperCase():i.toLowerCase(),s=Ln(r);if(n.body!==void 0){if(n.formEncType==="text/plain"){if(!de(o))return a();let m=typeof n.body=="string"?n.body:n.body instanceof FormData||n.body instanceof URLSearchParams?Array.from(n.body.entries()).reduce((g,v)=>{let[E,b]=v;return""+g+E+"="+b+`
`},""):String(n.body);return{path:r,submission:{formMethod:o,formAction:s,formEncType:n.formEncType,formData:void 0,json:void 0,text:m}}}else if(n.formEncType==="application/json"){if(!de(o))return a();try{let m=typeof n.body=="string"?JSON.parse(n.body):n.body;return{path:r,submission:{formMethod:o,formAction:s,formEncType:n.formEncType,formData:void 0,json:m,text:void 0}}}catch{return a()}}}k(typeof FormData=="function","FormData is not available in this environment");let l,u;if(n.formData)l=ir(n.formData),u=n.formData;else if(n.body instanceof FormData)l=ir(n.body),u=n.body;else if(n.body instanceof URLSearchParams)l=n.body,u=Yr(l);else if(n.body==null)l=new URLSearchParams,u=new FormData;else try{l=new URLSearchParams(n.body),u=Yr(l)}catch{return a()}let h={formMethod:o,formAction:s,formEncType:n&&n.formEncType||"application/x-www-form-urlencoded",formData:u,json:void 0,text:void 0};if(de(h.formMethod))return{path:r,submission:h};let c=Se(r);return t&&c.search&&hr(c.search)&&l.append("index",""),c.search="?"+l,{path:_e(c),submission:h}}function zr(e,t,r){r===void 0&&(r=!1);let n=e.findIndex(a=>a.route.id===t);return n>=0?e.slice(0,r?n+1:n):e}function Wr(e,t,r,n,a,i,o,s,l,u,h,c,m,g,v,E){let b=E?le(E[1])?E[1].error:E[1].data:void 0,D=e.createURL(t.location),R=e.createURL(a),P=r;i&&t.errors?P=zr(r,Object.keys(t.errors)[0],!0):E&&le(E[1])&&(P=zr(r,E[0]));let x=E?E[1].statusCode:void 0,A=o&&x&&x>=400,_=P.filter((C,$)=>{let{route:I}=C;if(I.lazy)return!0;if(I.loader==null)return!1;if(i)return ar(I,t.loaderData,t.errors);if(ai(t.loaderData,t.matches[$],C)||l.some(ae=>ae===C.route.id))return!0;let K=t.matches[$],W=C;return Jr(C,Y({currentUrl:D,currentParams:K.params,nextUrl:R,nextParams:W.params},n,{actionResult:b,actionStatus:x,defaultShouldRevalidate:A?!1:s||D.pathname+D.search===R.pathname+R.search||D.search!==R.search||Rn(K,W)}))}),y=[];return c.forEach((C,$)=>{if(i||!r.some(ne=>ne.route.id===C.routeId)||h.has($))return;let I=Ee(g,C.path,v);if(!I){y.push({key:$,routeId:C.routeId,path:C.path,matches:null,match:null,controller:null});return}let K=t.fetchers.get($),W=ot(I,C.path),ae=!1;m.has($)?ae=!1:u.has($)?(u.delete($),ae=!0):K&&K.state!=="idle"&&K.data===void 0?ae=s:ae=Jr(W,Y({currentUrl:D,currentParams:t.matches[t.matches.length-1].params,nextUrl:R,nextParams:r[r.length-1].params},n,{actionResult:b,actionStatus:x,defaultShouldRevalidate:A?!1:s})),ae&&y.push({key:$,routeId:C.routeId,path:C.path,matches:I,match:W,controller:new AbortController})}),[_,y]}function ar(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&t[e.id]!==void 0,a=r!=null&&r[e.id]!==void 0;return!n&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!a}function ai(e,t,r){let n=!t||r.route.id!==t.route.id,a=e[r.route.id]===void 0;return n||a}function Rn(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Jr(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Kr(e,t,r,n,a){var i;let o;if(e){let u=n[e];k(u,"No route found to patch children into: routeId = "+e),u.children||(u.children=[]),o=u.children}else o=r;let s=t.filter(u=>!o.some(h=>xn(u,h))),l=Ft(s,a,[e||"_","patch",String(((i=o)==null?void 0:i.length)||"0")],n);o.push(...l)}function xn(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var a;return(a=t.children)==null?void 0:a.some(i=>xn(r,i))}):!1}async function ii(e,t,r){if(!e.lazy)return;let n=await e.lazy();if(!e.lazy)return;let a=r[e.id];k(a,"No route found in manifest");let i={};for(let o in n){let l=a[o]!==void 0&&o!=="hasErrorBoundary";Ke(!l,'Route "'+a.id+'" has a static property "'+o+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+o+'" will be ignored.')),!l&&!Da.has(o)&&(i[o]=n[o])}Object.assign(a,i),Object.assign(a,Y({},t(a),{lazy:void 0}))}async function oi(e){let{matches:t}=e,r=t.filter(a=>a.shouldLoad);return(await Promise.all(r.map(a=>a.resolve()))).reduce((a,i,o)=>Object.assign(a,{[r[o].route.id]:i}),{})}async function li(e,t,r,n,a,i,o,s,l,u){let h=i.map(g=>g.route.lazy?ii(g.route,l,s):void 0),c=i.map((g,v)=>{let E=h[v],b=a.some(R=>R.route.id===g.route.id);return Y({},g,{shouldLoad:b,resolve:async R=>(R&&n.method==="GET"&&(g.route.lazy||g.route.loader)&&(b=!0),b?si(t,n,g,E,R,u):Promise.resolve({type:J.data,result:void 0}))})}),m=await e({matches:c,request:n,params:i[0].params,fetcherKey:o,context:u});try{await Promise.all(h)}catch{}return m}async function si(e,t,r,n,a,i){let o,s,l=u=>{let h,c=new Promise((v,E)=>h=E);s=()=>h(),t.signal.addEventListener("abort",s);let m=v=>typeof u!="function"?Promise.reject(new Error("You cannot call the handler for a route which defines a boolean "+('"'+e+'" [routeId: '+r.route.id+"]"))):u({request:t,params:r.params,context:i},...v!==void 0?[v]:[]),g=(async()=>{try{return{type:"data",result:await(a?a(E=>m(E)):m())}}catch(v){return{type:"error",result:v}}})();return Promise.race([g,c])};try{let u=r.route[e];if(n)if(u){let h,[c]=await Promise.all([l(u).catch(m=>{h=m}),n]);if(h!==void 0)throw h;o=c}else if(await n,u=r.route[e],u)o=await l(u);else if(e==="action"){let h=new URL(t.url),c=h.pathname+h.search;throw oe(405,{method:t.method,pathname:c,routeId:r.route.id})}else return{type:J.data,result:void 0};else if(u)o=await l(u);else{let h=new URL(t.url),c=h.pathname+h.search;throw oe(404,{pathname:c})}k(o.result!==void 0,"You defined "+(e==="action"?"an action":"a loader")+" for route "+('"'+r.route.id+"\" but didn't return anything from your `"+e+"` ")+"function. Please return a value or `null`.")}catch(u){return{type:J.error,result:u}}finally{s&&t.signal.removeEventListener("abort",s)}return o}async function ui(e){let{result:t,type:r}=e;if(Pn(t)){let c;try{let m=t.headers.get("Content-Type");m&&/\bapplication\/json\b/.test(m)?t.body==null?c=null:c=await t.json():c=await t.text()}catch(m){return{type:J.error,error:m}}return r===J.error?{type:J.error,error:new ke(t.status,t.statusText,c),statusCode:t.status,headers:t.headers}:{type:J.data,data:c,statusCode:t.status,headers:t.headers}}if(r===J.error){if(qr(t)){var n,a;if(t.data instanceof Error){var i,o;return{type:J.error,error:t.data,statusCode:(i=t.init)==null?void 0:i.status,headers:(o=t.init)!=null&&o.headers?new Headers(t.init.headers):void 0}}return{type:J.error,error:new ke(((n=t.init)==null?void 0:n.status)||500,void 0,t.data),statusCode:Ie(t)?t.status:void 0,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}}return{type:J.error,error:t,statusCode:Ie(t)?t.status:void 0}}if(mi(t)){var s,l;return{type:J.deferred,deferredData:t,statusCode:(s=t.init)==null?void 0:s.status,headers:((l=t.init)==null?void 0:l.headers)&&new Headers(t.init.headers)}}if(qr(t)){var u,h;return{type:J.data,data:t.data,statusCode:(u=t.init)==null?void 0:u.status,headers:(h=t.init)!=null&&h.headers?new Headers(t.init.headers):void 0}}return{type:J.data,data:t}}function ci(e,t,r,n,a,i){let o=e.headers.get("Location");if(k(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!dr.test(o)){let s=n.slice(0,n.findIndex(l=>l.route.id===r)+1);o=nr(new URL(t.url),s,a,!0,o,i),e.headers.set("Location",o)}return e}function Vr(e,t,r){if(dr.test(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),i=ue(a.pathname,r)!=null;if(a.origin===t.origin&&i)return a.pathname+a.search+a.hash}return e}function Je(e,t,r,n){let a=e.createURL(Ln(t)).toString(),i={signal:r};if(n&&de(n.formMethod)){let{formMethod:o,formEncType:s}=n;i.method=o.toUpperCase(),s==="application/json"?(i.headers=new Headers({"Content-Type":s}),i.body=JSON.stringify(n.json)):s==="text/plain"?i.body=n.text:s==="application/x-www-form-urlencoded"&&n.formData?i.body=ir(n.formData):i.body=n.formData}return new Request(a,i)}function ir(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function Yr(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function di(e,t,r,n,a){let i={},o=null,s,l=!1,u={},h=r&&le(r[1])?r[1].error:void 0;return e.forEach(c=>{if(!(c.route.id in t))return;let m=c.route.id,g=t[m];if(k(!je(g),"Cannot handle redirect results in processLoaderData"),le(g)){let v=g.error;h!==void 0&&(v=h,h=void 0),o=o||{};{let E=Ne(e,m);o[E.route.id]==null&&(o[E.route.id]=v)}i[m]=void 0,l||(l=!0,s=Ie(g.error)?g.error.status:500),g.headers&&(u[m]=g.headers)}else Ce(g)?(n.set(m,g.deferredData),i[m]=g.deferredData.data,g.statusCode!=null&&g.statusCode!==200&&!l&&(s=g.statusCode),g.headers&&(u[m]=g.headers)):(i[m]=g.data,g.statusCode&&g.statusCode!==200&&!l&&(s=g.statusCode),g.headers&&(u[m]=g.headers))}),h!==void 0&&r&&(o={[r[0]]:h},i[r[0]]=void 0),{loaderData:i,errors:o,statusCode:s||200,loaderHeaders:u}}function Xr(e,t,r,n,a,i,o){let{loaderData:s,errors:l}=di(t,r,n,o);return a.forEach(u=>{let{key:h,match:c,controller:m}=u,g=i[h];if(k(g,"Did not find corresponding fetcher result"),!(m&&m.signal.aborted))if(le(g)){let v=Ne(e.matches,c==null?void 0:c.route.id);l&&l[v.route.id]||(l=Y({},l,{[v.route.id]:g.error})),e.fetchers.delete(h)}else if(je(g))k(!1,"Unhandled fetcher revalidation redirect");else if(Ce(g))k(!1,"Unhandled fetcher deferred data");else{let v=De(g.data);e.fetchers.set(h,v)}}),{loaderData:s,errors:l}}function Gr(e,t,r,n){let a=Y({},t);for(let i of r){let o=i.route.id;if(t.hasOwnProperty(o)?t[o]!==void 0&&(a[o]=t[o]):e[o]!==void 0&&i.route.loader&&(a[o]=e[o]),n&&n.hasOwnProperty(o))break}return a}function Qr(e){return e?le(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Ne(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function Zr(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function oe(e,t){let{pathname:r,routeId:n,method:a,type:i,message:o}=t===void 0?{}:t,s="Unknown Server Error",l="Unknown @remix-run/router error";return e===400?(s="Bad Request",a&&r&&n?l="You made a "+a+' request to "'+r+'" but '+('did not provide a `loader` for route "'+n+'", ')+"so there is no way to handle the request.":i==="defer-action"?l="defer() is not supported in actions":i==="invalid-body"&&(l="Unable to encode submission body")):e===403?(s="Forbidden",l='Route "'+n+'" does not match URL "'+r+'"'):e===404?(s="Not Found",l='No route matches URL "'+r+'"'):e===405&&(s="Method Not Allowed",a&&r&&n?l="You made a "+a.toUpperCase()+' request to "'+r+'" but '+('did not provide an `action` for route "'+n+'", ')+"so there is no way to handle the request.":a&&(l='Invalid request method "'+a.toUpperCase()+'"')),new ke(e||500,s,new Error(l),!0)}function Rt(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,a]=t[r];if(je(a))return{key:n,result:a}}}function Ln(e){let t=typeof e=="string"?Se(e):e;return _e(Y({},t,{hash:""}))}function fi(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function hi(e){return Pn(e.result)&&ei.has(e.result.status)}function Ce(e){return e.type===J.deferred}function le(e){return e.type===J.error}function je(e){return(e&&e.type)===J.redirect}function qr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function mi(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}function Pn(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function pi(e){return qa.has(e.toLowerCase())}function de(e){return Qa.has(e.toLowerCase())}async function vi(e,t,r,n,a){let i=Object.entries(t);for(let o=0;o<i.length;o++){let[s,l]=i[o],u=e.find(m=>(m==null?void 0:m.route.id)===s);if(!u)continue;let h=n.find(m=>m.route.id===u.route.id),c=h!=null&&!Rn(h,u)&&(a&&a[u.route.id])!==void 0;Ce(l)&&c&&await fr(l,r,!1).then(m=>{m&&(t[s]=m)})}}async function yi(e,t,r){for(let n=0;n<r.length;n++){let{key:a,routeId:i,controller:o}=r[n],s=t[a];e.find(u=>(u==null?void 0:u.route.id)===i)&&Ce(s)&&(k(o,"Expected an AbortController for revalidating fetcher deferred result"),await fr(s,o.signal,!0).then(u=>{u&&(t[a]=u)}))}}async function fr(e,t,r){if(r===void 0&&(r=!1),!await e.deferredData.resolveData(t)){if(r)try{return{type:J.data,data:e.deferredData.unwrappedData}}catch(a){return{type:J.error,error:a}}return{type:J.data,data:e.deferredData.data}}}function hr(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function ot(e,t){let r=typeof t=="string"?Se(t).search:t.search;if(e[e.length-1].route.index&&hr(r||""))return e[e.length-1];let n=gn(e);return n[n.length-1]}function en(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:i,json:o}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(i!=null)return{formMethod:t,formAction:r,formEncType:n,formData:i,json:void 0,text:void 0};if(o!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:o,text:void 0}}}function Qt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function gi(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function nt(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function wi(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function De(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function Ei(e,t){try{let r=e.sessionStorage.getItem(Sn);if(r){let n=JSON.parse(r);for(let[a,i]of Object.entries(n||{}))i&&Array.isArray(i)&&t.set(a,new Set(i||[]))}}catch{}}function bi(e,t){if(t.size>0){let r={};for(let[n,a]of t)r[n]=[...a];try{e.sessionStorage.setItem(Sn,JSON.stringify(r))}catch(n){Ke(!1,"Failed to save applied view transitions in sessionStorage ("+n+").")}}}/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function jt(){return jt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jt.apply(this,arguments)}const Ye=d.createContext(null),st=d.createContext(null),kt=d.createContext(null),fe=d.createContext(null),mr=d.createContext(null),ve=d.createContext({outlet:null,matches:[],isDataRoute:!1}),Dn=d.createContext(null);function pr(e,t){let{relative:r}=t===void 0?{}:t;ut()||k(!1);let{basename:n,navigator:a}=d.useContext(fe),{hash:i,pathname:o,search:s}=ct(e,{relative:r}),l=o;return n!=="/"&&(l=o==="/"?n:me([n,o])),a.createHref({pathname:l,search:s,hash:i})}function ut(){return d.useContext(mr)!=null}function Re(){return ut()||k(!1),d.useContext(mr).location}function Cn(e){d.useContext(fe).static||d.useLayoutEffect(e)}function Si(){let{isDataRoute:e}=d.useContext(ve);return e?Hi():Ri()}function Ri(){ut()||k(!1);let e=d.useContext(Ye),{basename:t,future:r,navigator:n}=d.useContext(fe),{matches:a}=d.useContext(ve),{pathname:i}=Re(),o=JSON.stringify(ur(a,r.v7_relativeSplatPath)),s=d.useRef(!1);return Cn(()=>{s.current=!0}),d.useCallback(function(u,h){if(h===void 0&&(h={}),!s.current)return;if(typeof u=="number"){n.go(u);return}let c=cr(u,JSON.parse(o),i,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:me([t,c.pathname])),(h.replace?n.replace:n.push)(c,h.state,h)},[t,n,o,i,e])}const xi=d.createContext(null);function Li(e){let t=d.useContext(ve).outlet;return t&&d.createElement(xi.Provider,{value:e},t)}function ct(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=d.useContext(fe),{matches:a}=d.useContext(ve),{pathname:i}=Re(),o=JSON.stringify(ur(a,n.v7_relativeSplatPath));return d.useMemo(()=>cr(e,JSON.parse(o),i,r==="path"),[e,o,i,r])}function Pi(e,t,r,n){ut()||k(!1);let{navigator:a,static:i}=d.useContext(fe),{matches:o}=d.useContext(ve),s=o[o.length-1],l=s?s.params:{};s&&s.pathname;let u=s?s.pathnameBase:"/";s&&s.route;let h=Re(),c;c=h;let m=c.pathname||"/",g=m;if(u!=="/"){let b=u.replace(/^\//,"").split("/");g="/"+m.replace(/^\//,"").split("/").slice(b.length).join("/")}let v=!i&&r&&r.matches&&r.matches.length>0?r.matches:Ee(e,{pathname:g});return Oi(v&&v.map(b=>Object.assign({},b,{params:Object.assign({},l,b.params),pathname:me([u,a.encodeLocation?a.encodeLocation(b.pathname).pathname:b.pathname]),pathnameBase:b.pathnameBase==="/"?u:me([u,a.encodeLocation?a.encodeLocation(b.pathnameBase).pathname:b.pathnameBase])})),o,r,n)}function Di(){let e=On(),t=Ie(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return d.createElement(d.Fragment,null,d.createElement("h2",null,"Unexpected Application Error!"),d.createElement("h3",{style:{fontStyle:"italic"}},t),r?d.createElement("pre",{style:a},r):null,null)}const Ci=d.createElement(Di,null);class _i extends d.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?d.createElement(ve.Provider,{value:this.props.routeContext},d.createElement(Dn.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Ti(e){let{routeContext:t,match:r,children:n}=e,a=d.useContext(Ye);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),d.createElement(ve.Provider,{value:t},n)}function Oi(e,t,r,n){var a;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var i;if(!r)return null;if(r.errors)e=r.matches;else if((i=n)!=null&&i.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let o=e,s=(a=r)==null?void 0:a.errors;if(s!=null){let h=o.findIndex(c=>c.route.id&&(s==null?void 0:s[c.route.id])!==void 0);h>=0||k(!1),o=o.slice(0,Math.min(o.length,h+1))}let l=!1,u=-1;if(r&&n&&n.v7_partialHydration)for(let h=0;h<o.length;h++){let c=o[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(u=h),c.route.id){let{loaderData:m,errors:g}=r,v=c.route.loader&&m[c.route.id]===void 0&&(!g||g[c.route.id]===void 0);if(c.route.lazy||v){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((h,c,m)=>{let g,v=!1,E=null,b=null;r&&(g=s&&c.route.id?s[c.route.id]:void 0,E=c.route.errorElement||Ci,l&&(u<0&&m===0?(Bi("route-fallback"),v=!0,b=null):u===m&&(v=!0,b=c.route.hydrateFallbackElement||null)));let D=t.concat(o.slice(0,m+1)),R=()=>{let P;return g?P=E:v?P=b:c.route.Component?P=d.createElement(c.route.Component,null):c.route.element?P=c.route.element:P=h,d.createElement(Ti,{match:c,routeContext:{outlet:h,matches:D,isDataRoute:r!=null},children:P})};return r&&(c.route.ErrorBoundary||c.route.errorElement||m===0)?d.createElement(_i,{location:r.location,revalidation:r.revalidation,component:E,error:g,children:R(),routeContext:{outlet:null,matches:D,isDataRoute:!0}}):R()},null)}var _n=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(_n||{}),Tn=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Tn||{});function Mi(e){let t=d.useContext(Ye);return t||k(!1),t}function dt(e){let t=d.useContext(st);return t||k(!1),t}function Fi(e){let t=d.useContext(ve);return t||k(!1),t}function ft(e){let t=Fi(),r=t.matches[t.matches.length-1];return r.route.id||k(!1),r.route.id}function Ai(){return ft()}function Ni(){return dt().navigation}function ji(){let{matches:e,loaderData:t}=dt();return d.useMemo(()=>e.map(r=>pn(r,t)),[e,t])}function ki(){let e=dt(),t=ft();if(e.errors&&e.errors[t]!=null){console.error("You cannot `useLoaderData` in an errorElement (routeId: "+t+")");return}return e.loaderData[t]}function Ii(){let e=dt(),t=ft();return e.actionData?e.actionData[t]:void 0}function On(){var e;let t=d.useContext(Dn),r=dt(Tn.UseRouteError),n=ft();return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function Ui(){let e=d.useContext(kt);return e==null?void 0:e._data}function $i(){let e=d.useContext(kt);return e==null?void 0:e._error}function Hi(){let{router:e}=Mi(_n.UseNavigateStable),t=ft(),r=d.useRef(!1);return Cn(()=>{r.current=!0}),d.useCallback(function(a,i){i===void 0&&(i={}),r.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,jt({fromRouteId:t},i)))},[e,t])}const tn={};function Bi(e,t,r){tn[e]||(tn[e]=!0)}function zi(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Bl(e){return Li(e.context)}function Wi(e){let{basename:t="/",children:r=null,location:n,navigationType:a=Z.Pop,navigator:i,static:o=!1,future:s}=e;ut()&&k(!1);let l=t.replace(/^\/*/,"/"),u=d.useMemo(()=>({basename:l,navigator:i,static:o,future:jt({v7_relativeSplatPath:!1},s)}),[l,s,i,o]);typeof n=="string"&&(n=Se(n));let{pathname:h="/",search:c="",hash:m="",state:g=null,key:v="default"}=n,E=d.useMemo(()=>{let b=ue(h,l);return b==null?null:{location:{pathname:b,search:c,hash:m,state:g,key:v},navigationType:a}},[l,h,c,m,g,v,a]);return E==null?null:d.createElement(fe.Provider,{value:u},d.createElement(mr.Provider,{children:r,value:E}))}function Ji(e){let{children:t,errorElement:r,resolve:n}=e;return d.createElement(Vi,{resolve:n,errorElement:r},d.createElement(Yi,null,t))}var se=function(e){return e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error",e}(se||{});const Ki=new Promise(()=>{});class Vi extends d.Component{constructor(t){super(t),this.state={error:null}}static getDerivedStateFromError(t){return{error:t}}componentDidCatch(t,r){console.error("<Await> caught the following error during render",t,r)}render(){let{children:t,errorElement:r,resolve:n}=this.props,a=null,i=se.pending;if(!(n instanceof Promise))i=se.success,a=Promise.resolve(),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_data",{get:()=>n});else if(this.state.error){i=se.error;let o=this.state.error;a=Promise.reject().catch(()=>{}),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_error",{get:()=>o})}else n._tracked?(a=n,i="_error"in a?se.error:"_data"in a?se.success:se.pending):(i=se.pending,Object.defineProperty(n,"_tracked",{get:()=>!0}),a=n.then(o=>Object.defineProperty(n,"_data",{get:()=>o}),o=>Object.defineProperty(n,"_error",{get:()=>o})));if(i===se.error&&a._error instanceof Nt)throw Ki;if(i===se.error&&!r)throw a._error;if(i===se.error)return d.createElement(kt.Provider,{value:a,children:r});if(i===se.success)return d.createElement(kt.Provider,{value:a,children:t});throw a}}function Yi(e){let{children:t}=e,r=Ui(),n=typeof t=="function"?t(r):t;return d.createElement(d.Fragment,null,n)}function zl(e){let t={hasErrorBoundary:e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&Object.assign(t,{element:d.createElement(e.Component),Component:void 0}),e.HydrateFallback&&Object.assign(t,{hydrateFallbackElement:d.createElement(e.HydrateFallback),HydrateFallback:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:d.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pe(){return pe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pe.apply(this,arguments)}function vr(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}const Tt="get",Zt="application/x-www-form-urlencoded";function $t(e){return e!=null&&typeof e.tagName=="string"}function Xi(e){return $t(e)&&e.tagName.toLowerCase()==="button"}function Gi(e){return $t(e)&&e.tagName.toLowerCase()==="form"}function Qi(e){return $t(e)&&e.tagName.toLowerCase()==="input"}function Zi(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function qi(e,t){return e.button===0&&(!t||t==="_self")&&!Zi(e)}let xt=null;function eo(){if(xt===null)try{new FormData(document.createElement("form"),0),xt=!1}catch{xt=!0}return xt}const to=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function qt(e){return e!=null&&!to.has(e)?null:e}function ro(e,t){let r,n,a,i,o;if(Gi(e)){let s=e.getAttribute("action");n=s?ue(s,t):null,r=e.getAttribute("method")||Tt,a=qt(e.getAttribute("enctype"))||Zt,i=new FormData(e)}else if(Xi(e)||Qi(e)&&(e.type==="submit"||e.type==="image")){let s=e.form;if(s==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||s.getAttribute("action");if(n=l?ue(l,t):null,r=e.getAttribute("formmethod")||s.getAttribute("method")||Tt,a=qt(e.getAttribute("formenctype"))||qt(s.getAttribute("enctype"))||Zt,i=new FormData(s,e),!eo()){let{name:u,type:h,value:c}=e;if(h==="image"){let m=u?u+".":"";i.append(m+"x","0"),i.append(m+"y","0")}else u&&i.append(u,c)}}else{if($t(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Tt,n=null,a=Zt,o=e}return i&&a==="text/plain"&&(o=i,i=void 0),{action:n,method:r.toLowerCase(),encType:a,formData:i,body:o}}const no=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ao=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],io=["fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"],oo="6";try{window.__reactRouterVersion=oo}catch{}const Mn=d.createContext({isTransitioning:!1}),Fn=d.createContext(new Map),lo="startTransition",rn=mn[lo],so="flushSync",nn=xa[so],uo="useId",an=mn[uo];function co(e){rn?rn(e):e()}function at(e){nn?nn(e):e()}let fo=class{constructor(){this.status="pending",this.promise=new Promise((t,r)=>{this.resolve=n=>{this.status==="pending"&&(this.status="resolved",t(n))},this.reject=n=>{this.status==="pending"&&(this.status="rejected",r(n))}})}};function Jl(e){let{fallbackElement:t,router:r,future:n}=e,[a,i]=d.useState(r.state),[o,s]=d.useState(),[l,u]=d.useState({isTransitioning:!1}),[h,c]=d.useState(),[m,g]=d.useState(),[v,E]=d.useState(),b=d.useRef(new Map),{v7_startTransition:D}=n||{},R=d.useCallback(C=>{D?co(C):C()},[D]),P=d.useCallback((C,$)=>{let{deletedFetchers:I,flushSync:K,viewTransitionOpts:W}=$;C.fetchers.forEach((ne,ee)=>{ne.data!==void 0&&b.current.set(ee,ne.data)}),I.forEach(ne=>b.current.delete(ne));let ae=r.window==null||r.window.document==null||typeof r.window.document.startViewTransition!="function";if(!W||ae){K?at(()=>i(C)):R(()=>i(C));return}if(K){at(()=>{m&&(h&&h.resolve(),m.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:W.currentLocation,nextLocation:W.nextLocation})});let ne=r.window.document.startViewTransition(()=>{at(()=>i(C))});ne.finished.finally(()=>{at(()=>{c(void 0),g(void 0),s(void 0),u({isTransitioning:!1})})}),at(()=>g(ne));return}m?(h&&h.resolve(),m.skipTransition(),E({state:C,currentLocation:W.currentLocation,nextLocation:W.nextLocation})):(s(C),u({isTransitioning:!0,flushSync:!1,currentLocation:W.currentLocation,nextLocation:W.nextLocation}))},[r.window,m,h,b,R]);d.useLayoutEffect(()=>r.subscribe(P),[r,P]),d.useEffect(()=>{l.isTransitioning&&!l.flushSync&&c(new fo)},[l]),d.useEffect(()=>{if(h&&o&&r.window){let C=o,$=h.promise,I=r.window.document.startViewTransition(async()=>{R(()=>i(C)),await $});I.finished.finally(()=>{c(void 0),g(void 0),s(void 0),u({isTransitioning:!1})}),g(I)}},[R,o,h,r.window]),d.useEffect(()=>{h&&o&&a.location.key===o.location.key&&h.resolve()},[h,m,a.location,o]),d.useEffect(()=>{!l.isTransitioning&&v&&(s(v.state),u({isTransitioning:!0,flushSync:!1,currentLocation:v.currentLocation,nextLocation:v.nextLocation}),E(void 0))},[l.isTransitioning,v]),d.useEffect(()=>{},[]);let x=d.useMemo(()=>({createHref:r.createHref,encodeLocation:r.encodeLocation,go:C=>r.navigate(C),push:(C,$,I)=>r.navigate(C,{state:$,preventScrollReset:I==null?void 0:I.preventScrollReset}),replace:(C,$,I)=>r.navigate(C,{replace:!0,state:$,preventScrollReset:I==null?void 0:I.preventScrollReset})}),[r]),A=r.basename||"/",_=d.useMemo(()=>({router:r,navigator:x,static:!1,basename:A}),[r,x,A]),y=d.useMemo(()=>({v7_relativeSplatPath:r.future.v7_relativeSplatPath}),[r.future.v7_relativeSplatPath]);return d.useEffect(()=>zi(n,r.future),[n,r.future]),d.createElement(d.Fragment,null,d.createElement(Ye.Provider,{value:_},d.createElement(st.Provider,{value:a},d.createElement(Fn.Provider,{value:b.current},d.createElement(Mn.Provider,{value:l},d.createElement(Wi,{basename:A,location:a.location,navigationType:a.historyAction,navigator:x,future:y},a.initialized||r.future.v7_partialHydration?d.createElement(ho,{routes:r.routes,future:r.future,state:a}):t))))),null)}const ho=d.memo(mo);function mo(e){let{routes:t,future:r,state:n}=e;return Pi(t,void 0,n,r)}const po=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",vo=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,An=d.forwardRef(function(t,r){let{onClick:n,relative:a,reloadDocument:i,replace:o,state:s,target:l,to:u,preventScrollReset:h,viewTransition:c}=t,m=vr(t,no),{basename:g}=d.useContext(fe),v,E=!1;if(typeof u=="string"&&vo.test(u)&&(v=u,po))try{let P=new URL(window.location.href),x=u.startsWith("//")?new URL(P.protocol+u):new URL(u),A=ue(x.pathname,g);x.origin===P.origin&&A!=null?u=A+x.search+x.hash:E=!0}catch{}let b=pr(u,{relative:a}),D=go(u,{replace:o,state:s,target:l,preventScrollReset:h,relative:a,viewTransition:c});function R(P){n&&n(P),P.defaultPrevented||D(P)}return d.createElement("a",pe({},m,{href:v||b,onClick:E||i?n:R,ref:r,target:l}))}),yo=d.forwardRef(function(t,r){let{"aria-current":n="page",caseSensitive:a=!1,className:i="",end:o=!1,style:s,to:l,viewTransition:u,children:h}=t,c=vr(t,ao),m=ct(l,{relative:c.relative}),g=Re(),v=d.useContext(st),{navigator:E,basename:b}=d.useContext(fe),D=v!=null&&xo(m)&&u===!0,R=E.encodeLocation?E.encodeLocation(m).pathname:m.pathname,P=g.pathname,x=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;a||(P=P.toLowerCase(),x=x?x.toLowerCase():null,R=R.toLowerCase()),x&&b&&(x=ue(x,b)||x);const A=R!=="/"&&R.endsWith("/")?R.length-1:R.length;let _=P===R||!o&&P.startsWith(R)&&P.charAt(A)==="/",y=x!=null&&(x===R||!o&&x.startsWith(R)&&x.charAt(R.length)==="/"),C={isActive:_,isPending:y,isTransitioning:D},$=_?n:void 0,I;typeof i=="function"?I=i(C):I=[i,_?"active":null,y?"pending":null,D?"transitioning":null].filter(Boolean).join(" ");let K=typeof s=="function"?s(C):s;return d.createElement(An,pe({},c,{"aria-current":$,className:I,ref:r,style:K,to:l,viewTransition:u}),typeof h=="function"?h(C):h)}),Nn=d.forwardRef((e,t)=>{let{fetcherKey:r,navigate:n,reloadDocument:a,replace:i,state:o,method:s=Tt,action:l,onSubmit:u,relative:h,preventScrollReset:c,viewTransition:m}=e,g=vr(e,io),v=In(),E=bo(l,{relative:h}),b=s.toLowerCase()==="get"?"get":"post",D=R=>{if(u&&u(R),R.defaultPrevented)return;R.preventDefault();let P=R.nativeEvent.submitter,x=(P==null?void 0:P.getAttribute("formmethod"))||s;v(P||R.currentTarget,{fetcherKey:r,method:x,navigate:n,replace:i,state:o,relative:h,preventScrollReset:c,viewTransition:m})};return d.createElement("form",pe({ref:t,method:b,action:E,onSubmit:a?u:D},g))});var Ve;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ve||(Ve={}));var It;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(It||(It={}));function Ht(e){let t=d.useContext(Ye);return t||k(!1),t}function jn(e){let t=d.useContext(st);return t||k(!1),t}function go(e,t){let{target:r,replace:n,state:a,preventScrollReset:i,relative:o,viewTransition:s}=t===void 0?{}:t,l=Si(),u=Re(),h=ct(e,{relative:o});return d.useCallback(c=>{if(qi(c,r)){c.preventDefault();let m=n!==void 0?n:_e(u)===_e(h);l(e,{replace:m,state:a,preventScrollReset:i,relative:o,viewTransition:s})}},[u,l,h,n,a,r,e,i,o,s])}function wo(){if(typeof document>"u")throw new Error("You are calling submit during the server render. Try calling submit within a `useEffect` or callback instead.")}let Eo=0,kn=()=>"__"+String(++Eo)+"__";function In(){let{router:e}=Ht(Ve.UseSubmit),{basename:t}=d.useContext(fe),r=Ai();return d.useCallback(function(n,a){a===void 0&&(a={}),wo();let{action:i,method:o,encType:s,formData:l,body:u}=ro(n,t);if(a.navigate===!1){let h=a.fetcherKey||kn();e.fetch(h,r,a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:u,formMethod:a.method||o,formEncType:a.encType||s,flushSync:a.flushSync})}else e.navigate(a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:u,formMethod:a.method||o,formEncType:a.encType||s,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}function bo(e,t){let{relative:r}=t===void 0?{}:t,{basename:n}=d.useContext(fe),a=d.useContext(ve);a||k(!1);let[i]=a.matches.slice(-1),o=pe({},ct(e||".",{relative:r})),s=Re();if(e==null){o.search=s.search;let l=new URLSearchParams(o.search),u=l.getAll("index");if(u.some(c=>c==="")){l.delete("index"),u.filter(m=>m).forEach(m=>l.append("index",m));let c=l.toString();o.search=c?"?"+c:""}}return(!e||e===".")&&i.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(o.pathname=o.pathname==="/"?n:me([n,o.pathname])),_e(o)}function So(e){var t;let{key:r}=e===void 0?{}:e,{router:n}=Ht(Ve.UseFetcher),a=jn(It.UseFetcher),i=d.useContext(Fn),o=d.useContext(ve),s=(t=o.matches[o.matches.length-1])==null?void 0:t.route.id;i||k(!1),o||k(!1),s==null&&k(!1);let l=an?an():"",[u,h]=d.useState(r||l);r&&r!==u?h(r):u||h(kn()),d.useEffect(()=>(n.getFetcher(u),()=>{n.deleteFetcher(u)}),[n,u]);let c=d.useCallback((R,P)=>{s||k(!1),n.fetch(u,s,R,P)},[u,s,n]),m=In(),g=d.useCallback((R,P)=>{m(R,pe({},P,{navigate:!1,fetcherKey:u}))},[u,m]),v=d.useMemo(()=>d.forwardRef((P,x)=>d.createElement(Nn,pe({},P,{navigate:!1,fetcherKey:u,ref:x}))),[u]),E=a.fetchers.get(u)||bn,b=i.get(u);return d.useMemo(()=>pe({Form:v,submit:g,load:c},E,{data:b}),[v,g,c,E,b])}const on="react-router-scroll-positions";let Lt={};function Kl(e){let{getKey:t,storageKey:r}=e===void 0?{}:e,{router:n}=Ht(Ve.UseScrollRestoration),{restoreScrollPosition:a,preventScrollReset:i}=jn(It.UseScrollRestoration),{basename:o}=d.useContext(fe),s=Re(),l=ji(),u=Ni();d.useEffect(()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"}),[]),Ro(d.useCallback(()=>{if(u.state==="idle"){let h=(t?t(s,l):null)||s.key;Lt[h]=window.scrollY}try{sessionStorage.setItem(r||on,JSON.stringify(Lt))}catch{}window.history.scrollRestoration="auto"},[r,t,u.state,s,l])),typeof document<"u"&&(d.useLayoutEffect(()=>{try{let h=sessionStorage.getItem(r||on);h&&(Lt=JSON.parse(h))}catch{}},[r]),d.useLayoutEffect(()=>{let h=t&&o!=="/"?(m,g)=>t(pe({},m,{pathname:ue(m.pathname,o)||m.pathname}),g):t,c=n==null?void 0:n.enableScrollRestoration(Lt,()=>window.scrollY,h);return()=>c&&c()},[n,o,t]),d.useLayoutEffect(()=>{if(a!==!1){if(typeof a=="number"){window.scrollTo(0,a);return}if(s.hash){let h=document.getElementById(decodeURIComponent(s.hash.slice(1)));if(h){h.scrollIntoView();return}}i!==!0&&window.scrollTo(0,0)}},[s,a,i]))}function Ro(e,t){let{capture:r}={};d.useEffect(()=>{let n=r!=null?{capture:r}:void 0;return window.addEventListener("pagehide",e,n),()=>{window.removeEventListener("pagehide",e,n)}},[e,r])}function xo(e,t){t===void 0&&(t={});let r=d.useContext(Mn);r==null&&k(!1);let{basename:n}=Ht(Ve.useViewTransitionState),a=ct(e,{relative:t.relative});if(!r.isTransitioning)return!1;let i=ue(r.currentLocation.pathname,n)||r.currentLocation.pathname,o=ue(r.nextLocation.pathname,n)||r.nextLocation.pathname;return At(a.pathname,o)!=null||At(a.pathname,i)!=null}var Lo=-1,Po=-2,Do=-3,Co=-4,_o=-5,To=-6,Oo=-7,Mo="B",Fo="D",Un="E",Ao="M",No="N",$n="P",jo="R",ko="S",Io="Y",Uo="U",$o="Z",Hn=class{constructor(){St(this,"promise");St(this,"resolve");St(this,"reject");this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};function Ho(){const e=new TextDecoder;let t="";return new TransformStream({transform(r,n){const a=e.decode(r,{stream:!0}),i=(t+a).split(`
`);t=i.pop()||"";for(const o of i)n.enqueue(o)},flush(r){t&&r.enqueue(t)}})}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var er=typeof window<"u"?window:typeof globalThis<"u"?globalThis:void 0;function or(e){const{hydrated:t,values:r}=this;if(typeof e=="number")return ln.call(this,e);if(!Array.isArray(e)||!e.length)throw new SyntaxError;const n=r.length;for(const a of e)r.push(a);return t.length=r.length,ln.call(this,n)}function ln(e){const{hydrated:t,values:r,deferred:n,plugins:a}=this;let i;const o=[[e,l=>{i=l}]];let s=[];for(;o.length>0;){const[l,u]=o.pop();switch(l){case Oo:u(void 0);continue;case _o:u(null);continue;case Po:u(NaN);continue;case To:u(1/0);continue;case Do:u(-1/0);continue;case Co:u(-0);continue}if(t[l]){u(t[l]);continue}const h=r[l];if(!h||typeof h!="object"){t[l]=h,u(h);continue}if(Array.isArray(h))if(typeof h[0]=="string"){const[c,m,g]=h;switch(c){case Fo:u(t[l]=new Date(m));continue;case Uo:u(t[l]=new URL(m));continue;case Mo:u(t[l]=BigInt(m));continue;case jo:u(t[l]=new RegExp(m,g));continue;case Io:u(t[l]=Symbol.for(m));continue;case ko:const v=new Set;t[l]=v;for(let x=1;x<h.length;x++)o.push([h[x],A=>{v.add(A)}]);u(v);continue;case Ao:const E=new Map;t[l]=E;for(let x=1;x<h.length;x+=2){const A=[];o.push([h[x+1],_=>{A[1]=_}]),o.push([h[x],_=>{A[0]=_}]),s.push(()=>{E.set(A[0],A[1])})}u(E);continue;case No:const b=Object.create(null);t[l]=b;for(const x of Object.keys(m).reverse()){const A=[];o.push([m[x],_=>{A[1]=_}]),o.push([Number(x.slice(1)),_=>{A[0]=_}]),s.push(()=>{b[A[0]]=A[1]})}u(b);continue;case $n:if(t[m])u(t[l]=t[m]);else{const x=new Hn;n[m]=x,u(t[l]=x.promise)}continue;case Un:const[,D,R]=h;let P=R&&er&&er[R]?new er[R](D):new Error(D);t[l]=P,u(P);continue;case $o:u(t[l]=t[m]);continue;default:if(Array.isArray(a)){const x=[],A=h.slice(1);for(let _=0;_<A.length;_++){const y=A[_];o.push([y,C=>{x[_]=C}])}s.push(()=>{for(const _ of a){const y=_(h[0],...x);if(y){u(t[l]=y.value);return}}throw new SyntaxError});continue}throw new SyntaxError}}else{const c=[];t[l]=c;for(let m=0;m<h.length;m++){const g=h[m];g!==Lo&&o.push([g,v=>{c[m]=v}])}u(c);continue}else{const c={};t[l]=c;for(const m of Object.keys(h).reverse()){const g=[];o.push([h[m],v=>{g[1]=v}]),o.push([Number(m.slice(1)),v=>{g[0]=v}]),s.push(()=>{c[g[0]]=g[1]})}u(c);continue}}for(;s.length>0;)s.pop()();return i}async function Bo(e,t){const{plugins:r}=t??{},n=new Hn,a=e.pipeThrough(Ho()).getReader(),i={values:[],hydrated:[],deferred:{},plugins:r},o=await zo.call(i,a);let s=n.promise;return o.done?n.resolve():s=Wo.call(i,a).then(n.resolve).catch(l=>{for(const u of Object.values(i.deferred))u.reject(l);n.reject(l)}),{done:s.then(()=>a.closed),value:o.value}}async function zo(e){const t=await e.read();if(!t.value)throw new SyntaxError;let r;try{r=JSON.parse(t.value)}catch{throw new SyntaxError}return{done:t.done,value:or.call(this,r)}}async function Wo(e){let t=await e.read();for(;!t.done;){if(!t.value)continue;const r=t.value;switch(r[0]){case $n:{const n=r.indexOf(":"),a=Number(r.slice(1,n)),i=this.deferred[a];if(!i)throw new Error(`Deferred ID ${a} not found in stream`);const o=r.slice(n+1);let s;try{s=JSON.parse(o)}catch{throw new SyntaxError}const l=or.call(this,s);i.resolve(l);break}case Un:{const n=r.indexOf(":"),a=Number(r.slice(1,n)),i=this.deferred[a];if(!i)throw new Error(`Deferred ID ${a} not found in stream`);const o=r.slice(n+1);let s;try{s=JSON.parse(o)}catch{throw new SyntaxError}const l=or.call(this,s);i.reject(l);break}default:throw new SyntaxError}t=await e.read()}}/**
 * @remix-run/server-runtime v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Bn=Symbol("SingleFetchRedirect");/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function q(){return q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},q.apply(this,arguments)}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function be(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */async function zn(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__remixContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Jo(e,t,r){let n=e.map(i=>{var o;let s=t[i.route.id],l=r.routes[i.route.id];return[l.css?l.css.map(u=>({rel:"stylesheet",href:u})):[],(s==null||(o=s.links)===null||o===void 0?void 0:o.call(s))||[]]}).flat(2),a=Qo(e,r);return Jn(n,a)}async function Wn(e,t){var r,n;if(!e.css&&!t.links||!qo())return;let a=[((r=e.css)===null||r===void 0?void 0:r.map(s=>({rel:"stylesheet",href:s})))??[],((n=t.links)===null||n===void 0?void 0:n.call(t))??[]].flat(1);if(a.length===0)return;let i=[];for(let s of a)!yr(s)&&s.rel==="stylesheet"&&i.push({...s,rel:"preload",as:"style"});let o=i.filter(s=>(!s.media||window.matchMedia(s.media).matches)&&!document.querySelector(`link[rel="stylesheet"][href="${s.href}"]`));await Promise.all(o.map(Ko))}async function Ko(e){return new Promise(t=>{let r=document.createElement("link");Object.assign(r,e);function n(){document.head.contains(r)&&document.head.removeChild(r)}r.onload=()=>{n(),t()},r.onerror=()=>{n(),t()},document.head.appendChild(r)})}function yr(e){return e!=null&&typeof e.page=="string"}function Vo(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Yo(e,t,r){let n=await Promise.all(e.map(async a=>{let i=await zn(t.routes[a.route.id],r);return i.links?i.links():[]}));return Jn(n.flat(1).filter(Vo).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function sn(e,t,r,n,a,i,o){let s=Kn(e),l=(c,m)=>r[m]?c.route.id!==r[m].route.id:!0,u=(c,m)=>{var g;return r[m].pathname!==c.pathname||((g=r[m].route.path)===null||g===void 0?void 0:g.endsWith("*"))&&r[m].params["*"]!==c.params["*"]};return o==="data"&&(i.v3_singleFetch||a.search!==s.search)?t.filter((c,m)=>{if(!n.routes[c.route.id].hasLoader)return!1;if(l(c,m)||u(c,m))return!0;let v=i.v3_singleFetch||a.search!==s.search;if(c.route.shouldRevalidate){var E;let b=c.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:((E=r[0])===null||E===void 0?void 0:E.params)||{},nextUrl:new URL(e,window.origin),nextParams:c.params,defaultShouldRevalidate:v});if(typeof b=="boolean")return b}return v}):t.filter((c,m)=>{let g=n.routes[c.route.id];return(o==="assets"||g.hasLoader)&&(l(c,m)||u(c,m))})}function Xo(e,t,r){let n=Kn(e);return gr(t.filter(a=>r.routes[a.route.id].hasLoader&&!r.routes[a.route.id].hasClientLoader).map(a=>{let{pathname:i,search:o}=n,s=new URLSearchParams(o);return s.set("_data",a.route.id),`${i}?${s}`}))}function Go(e,t){return gr(e.map(r=>{let n=t.routes[r.route.id],a=[n.module];return n.imports&&(a=a.concat(n.imports)),a}).flat(1))}function Qo(e,t){return gr(e.map(r=>{let n=t.routes[r.route.id],a=[n.module];return n.imports&&(a=a.concat(n.imports)),a}).flat(1))}function gr(e){return[...new Set(e)]}function Zo(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function Jn(e,t){let r=new Set,n=new Set(t);return e.reduce((a,i)=>{if(t&&!yr(i)&&i.as==="script"&&i.href&&n.has(i.href))return a;let s=JSON.stringify(Zo(i));return r.has(s)||(r.add(s),a.push({key:s,link:i})),a},[])}function Kn(e){let t=Se(e);return t.search===void 0&&(t.search=""),t}let Pt;function qo(){if(Pt!==void 0)return Pt;let e=document.createElement("link");return Pt=e.relList.supports("preload"),e=null,Pt}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const el={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},tl=/[&><\u2028\u2029]/g;function Dt(e){return e.replace(tl,t=>el[t])}function un(e){return{__html:e}}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function rl(e){return e.headers.get("X-Remix-Catch")!=null}function nl(e){return e.headers.get("X-Remix-Error")!=null}function al(e){return wr(e)&&e.status>=400&&e.headers.get("X-Remix-Error")==null&&e.headers.get("X-Remix-Catch")==null&&e.headers.get("X-Remix-Response")==null}function il(e){return e.headers.get("X-Remix-Redirect")!=null}function ol(e){var t;return!!((t=e.headers.get("Content-Type"))!==null&&t!==void 0&&t.match(/text\/remix-deferred/))}function wr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function ll(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}async function Vn(e,t,r=0){let n=new URL(e.url);n.searchParams.set("_data",t),r>0&&await new Promise(s=>setTimeout(s,5**r*10));let a=await Bt(e),i=window.__remixRevalidation,o=await fetch(n.href,a).catch(s=>{if(typeof i=="number"&&i===window.__remixRevalidation&&(s==null?void 0:s.name)==="TypeError"&&r<3)return Vn(e,t,r+1);throw s});if(nl(o)){let s=await o.json(),l=new Error(s.message);return l.stack=s.stack,l}if(al(o)){let s=await o.text(),l=new Error(s);return l.stack=void 0,l}return o}async function Bt(e){let t={signal:e.signal};if(e.method!=="GET"){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}const sl="__deferred_promise:";async function ul(e){if(!e)throw new Error("parseDeferredReadableStream requires stream argument");let t,r={};try{let n=cl(e),i=(await n.next()).value;if(!i)throw new Error("no critical data");let o=JSON.parse(i);if(typeof o=="object"&&o!==null)for(let[s,l]of Object.entries(o))typeof l!="string"||!l.startsWith(sl)||(t=t||{},t[s]=new Promise((u,h)=>{r[s]={resolve:c=>{u(c),delete r[s]},reject:c=>{h(c),delete r[s]}}}));return(async()=>{try{for await(let s of n){let[l,...u]=s.split(":"),h=u.join(":"),c=JSON.parse(h);if(l==="data")for(let[m,g]of Object.entries(c))r[m]&&r[m].resolve(g);else if(l==="error")for(let[m,g]of Object.entries(c)){let v=new Error(g.message);v.stack=g.stack,r[m]&&r[m].reject(v)}}for(let[s,l]of Object.entries(r))l.reject(new Nt(`Deferred ${s} will never be resolved`))}catch(s){for(let l of Object.values(r))l.reject(s)}})(),new Ya({...o,...t})}catch(n){for(let a of Object.values(r))a.reject(n);throw n}}async function*cl(e){let t=e.getReader(),r=[],n=[],a=!1,i=new TextEncoder,o=new TextDecoder,s=async()=>{if(n.length>0)return n.shift();for(;!a&&n.length===0;){let u=await t.read();if(u.done){a=!0;break}r.push(u.value);try{let c=o.decode(cn(...r)).split(`

`);if(c.length>=2&&(n.push(...c.slice(0,-1)),r=[i.encode(c.slice(-1).join(`

`))]),n.length>0)break}catch{continue}}return n.length>0||r.length>0&&(n=o.decode(cn(...r)).split(`

`).filter(h=>h),r=[]),n.shift()},l=await s();for(;l;)yield l,l=await s()}function cn(...e){let t=new Uint8Array(e.reduce((n,a)=>n+a.length,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Vl(e,t,r){return async({request:n,matches:a,fetcherKey:i})=>n.method!=="GET"?dl(n,a):i?hl(n,a):fl(e,t,r(),n,a)}async function dl(e,t){let r=t.find(i=>i.shouldLoad);be(r,"No action match found");let n,a=await r.resolve(async i=>await i(async()=>{let s=zt(e.url),l=await Bt(e),{data:u,status:h}=await Er(s,l);return n=h,lr(u,r.route.id)}));return wr(a.result)||Ie(a.result)?{[r.route.id]:a}:{[r.route.id]:{type:a.type,result:Va(a.result,n)}}}async function fl(e,t,r,n,a){let i=new Set,o=!1,s=a.map(()=>dn()),l=Promise.all(s.map(v=>v.promise)),u=dn(),h=Xn(zt(n.url)),c=await Bt(n),m={},g=Promise.all(a.map(async(v,E)=>v.resolve(async b=>{if(s[E].resolve(),!v.shouldLoad){var D;if(!r.state.initialized)return;if(v.route.id in r.state.loaderData&&e.routes[v.route.id]&&(D=t[v.route.id])!==null&&D!==void 0&&D.shouldRevalidate){e.routes[v.route.id].hasLoader&&(o=!0);return}}if(e.routes[v.route.id].hasClientLoader){e.routes[v.route.id].hasLoader&&(o=!0);try{let R=await Yn(b,h,c,v.route.id);m[v.route.id]={type:"data",result:R}}catch(R){m[v.route.id]={type:"error",result:R}}return}e.routes[v.route.id].hasLoader&&i.add(v.route.id);try{let R=await b(async()=>{let P=await u.promise;return Gn(P,v.route.id)});m[v.route.id]={type:"data",result:R}}catch(R){m[v.route.id]={type:"error",result:R}}})));if(await l,(!r.state.initialized||i.size===0)&&!window.__remixHdrActive)u.resolve({});else try{o&&i.size>0&&h.searchParams.set("_routes",a.filter(E=>i.has(E.route.id)).map(E=>E.route.id).join(","));let v=await Er(h,c);u.resolve(v.data)}catch(v){u.reject(v)}return await g,m}async function hl(e,t){let r=t.find(a=>a.shouldLoad);be(r,"No fetcher match found");let n=await r.resolve(async a=>{let i=Xn(zt(e.url)),o=await Bt(e);return Yn(a,i,o,r.route.id)});return{[r.route.id]:n}}function Yn(e,t,r,n){return e(async()=>{let a=new URL(t);a.searchParams.set("_routes",n);let{data:i}=await Er(a,r);return Gn(i,n)})}function Xn(e){let t=e.searchParams.getAll("index");e.searchParams.delete("index");let r=[];for(let n of t)n&&r.push(n);for(let n of r)e.searchParams.append("index",n);return e}function zt(e){let t=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return t.pathname==="/"?t.pathname="_root.data":t.pathname=`${t.pathname.replace(/\/$/,"")}.data`,t}async function Er(e,t){let r=await fetch(e,t);if(new Set([100,101,204,205]).has(r.status))return!t.method||t.method==="GET"?{status:r.status,data:{}}:{status:r.status,data:{data:null}};be(r.body,"No response body to decode");try{let a=await ml(r.body,window);return{status:r.status,data:a.value}}catch(a){throw console.error(a),new Error(`Unable to decode turbo-stream response from URL: ${e.toString()}`)}}function ml(e,t){return Bo(e,{plugins:[(r,...n)=>{if(r==="SanitizedError"){let[a,i,o]=n,s=Error;a&&a in t&&typeof t[a]=="function"&&(s=t[a]);let l=new s(i);return l.stack=o,{value:l}}if(r==="ErrorResponse"){let[a,i,o]=n;return{value:new ke(i,o,a)}}if(r==="SingleFetchRedirect")return{value:{[Bn]:n[0]}}},(r,n)=>{if(r==="SingleFetchFallback")return{value:void 0};if(r==="SingleFetchClassInstance")return{value:n}}]})}function Gn(e,t){let r=e[Bn];return r?lr(r,t):e[t]!==void 0?lr(e[t],t):null}function lr(e,t){if("error"in e)throw e.error;if("redirect"in e){let r={};throw e.revalidate&&(r["X-Remix-Revalidate"]="yes"),e.reload&&(r["X-Remix-Reload-Document"]="yes"),e.replace&&(r["X-Remix-Replace"]="yes"),wn(e.redirect,{status:e.status,headers:r})}else{if("data"in e)return e.data;throw new Error(`No response found for routeId "${t}"`)}}function dn(){let e,t,r=new Promise((n,a)=>{e=async i=>{n(i);try{await r}catch{}},t=async i=>{a(i);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */class Yl extends d.Component{constructor(t){super(t),this.state={error:t.error||null,location:t.location}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location?{error:t.error||null,location:t.location}:{error:t.error||r.error,location:r.location}}render(){return this.state.error?d.createElement(Qn,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}}function Qn({error:e,isOutsideRemixApp:t}){console.error(e);let r=d.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information."
        );
      `}});if(Ie(e))return d.createElement(sr,{title:"Unhandled Thrown Response!"},d.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),r);let n;if(e instanceof Error)n=e;else{let a=e==null?"Unknown Error":typeof e=="object"&&"toString"in e?e.toString():JSON.stringify(e);n=new Error(a)}return d.createElement(sr,{title:"Application Error!",isOutsideRemixApp:t},d.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),d.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},n.stack),r)}function sr({title:e,renderScripts:t,isOutsideRemixApp:r,children:n}){var a;let{routeModules:i}=Xe();return(a=i.root)!==null&&a!==void 0&&a.Layout&&!r?n:d.createElement("html",{lang:"en"},d.createElement("head",null,d.createElement("meta",{charSet:"utf-8"}),d.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),d.createElement("title",null,e)),d.createElement("body",null,d.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},n,t?d.createElement(Nl,null):null)))}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pl(){return d.createElement(sr,{title:"Loading...",renderScripts:!0},d.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "💿 Hey developer 👋. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://remix.run/route/hydrate-fallback " +
                "for more information."
              );
            `}}))}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Zn(e){let t={};return Object.values(e).forEach(r=>{let n=r.parentId||"";t[n]||(t[n]=[]),t[n].push(r)}),t}function vl(e,t,r){let n=qn(t),a=t.HydrateFallback&&(!r||e.id==="root")?t.HydrateFallback:e.id==="root"?pl:void 0,i=t.ErrorBoundary?t.ErrorBoundary:e.id==="root"?()=>d.createElement(Qn,{error:On()}):void 0;return e.id==="root"&&t.Layout?{...n?{element:d.createElement(t.Layout,null,d.createElement(n,null))}:{Component:n},...i?{errorElement:d.createElement(t.Layout,null,d.createElement(i,null))}:{ErrorBoundary:i},...a?{hydrateFallbackElement:d.createElement(t.Layout,null,d.createElement(a,null))}:{HydrateFallback:a}}:{Component:n,ErrorBoundary:i,HydrateFallback:a}}function Xl(e,t,r,n,a,i){return br(t,r,n,a,i,"",Zn(t),e)}function Ct(e,t,r){if(r){let o=`You cannot call ${e==="action"?"serverAction()":"serverLoader()"} in SPA Mode (routeId: "${t.id}")`;throw console.error(o),new ke(400,"Bad Request",new Error(o),!0)}let a=`You are trying to call ${e==="action"?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;if(e==="loader"&&!t.hasLoader||e==="action"&&!t.hasAction)throw console.error(a),new ke(400,"Bad Request",new Error(a),!0)}function tr(e,t){let r=e==="clientAction"?"a":"an",n=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(n),new ke(405,"Method Not Allowed",new Error(n),!0)}function br(e,t,r,n,a,i="",o=Zn(e),s){return(o[i]||[]).map(l=>{let u=t[l.id];async function h(P,x,A){if(typeof A=="function")return await A();let _=await wl(P,l);return x?El(_):_}function c(P,x,A){return l.hasLoader?h(P,x,A):Promise.resolve(null)}function m(P,x,A){if(!l.hasAction)throw tr("action",l.id);return h(P,x,A)}async function g(P){let x=t[l.id],A=x?Wn(l,x):Promise.resolve();try{return P()}finally{await A}}let v={id:l.id,index:l.index,path:l.path};if(u){var E,b,D;Object.assign(v,{...v,...vl(l,u,a),handle:u.handle,shouldRevalidate:fn(n,u,l.id,s)});let P=r==null||(E=r.loaderData)===null||E===void 0?void 0:E[l.id],x=r==null||(b=r.errors)===null||b===void 0?void 0:b[l.id],A=s==null&&(((D=u.clientLoader)===null||D===void 0?void 0:D.hydrate)===!0||!l.hasLoader);v.loader=async({request:_,params:y},C)=>{try{return await g(async()=>(be(u,"No `routeModule` available for critical-route loader"),u.clientLoader?u.clientLoader({request:_,params:y,async serverLoader(){if(Ct("loader",l,a),A){if(P!==void 0)return P;if(x!==void 0)throw x;return null}return c(_,!0,C)}}):a?null:c(_,!1,C)))}finally{A=!1}},v.loader.hydrate=Sl(l,u,a),v.action=({request:_,params:y},C)=>g(async()=>{if(be(u,"No `routeModule` available for critical-route action"),!u.clientAction){if(a)throw tr("clientAction",l.id);return m(_,!1,C)}return u.clientAction({request:_,params:y,async serverAction(){return Ct("action",l,a),m(_,!0,C)}})})}else l.hasClientLoader||(v.loader=({request:P},x)=>g(()=>a?Promise.resolve(null):c(P,!1,x))),l.hasClientAction||(v.action=({request:P},x)=>g(()=>{if(a)throw tr("clientAction",l.id);return m(P,!1,x)})),v.lazy=async()=>{let P=await gl(l,t),x={...P};if(P.clientLoader){let A=P.clientLoader;x.loader=(_,y)=>A({..._,async serverLoader(){return Ct("loader",l,a),c(_.request,!0,y)}})}if(P.clientAction){let A=P.clientAction;x.action=(_,y)=>A({..._,async serverAction(){return Ct("action",l,a),m(_.request,!0,y)}})}return{...x.loader?{loader:x.loader}:{},...x.action?{action:x.action}:{},hasErrorBoundary:x.hasErrorBoundary,shouldRevalidate:fn(n,x,l.id,s),handle:x.handle,Component:x.Component,ErrorBoundary:x.ErrorBoundary}};let R=br(e,t,r,n,a,l.id,o,s);return R.length>0&&(v.children=R),v})}function fn(e,t,r,n){if(n)return yl(r,t.shouldRevalidate,n);if(e.v3_singleFetch&&t.shouldRevalidate){let a=t.shouldRevalidate;return i=>a({...i,defaultShouldRevalidate:!0})}return t.shouldRevalidate}function yl(e,t,r){let n=!1;return a=>n?t?t(a):a.defaultShouldRevalidate:(n=!0,r.has(e))}async function gl(e,t){let r=await zn(e,t);return await Wn(e,r),{Component:qn(r),ErrorBoundary:r.ErrorBoundary,clientAction:r.clientAction,clientLoader:r.clientLoader,handle:r.handle,links:r.links,meta:r.meta,shouldRevalidate:r.shouldRevalidate}}async function wl(e,t){let r=await Vn(e,t.id);if(r instanceof Error)throw r;if(il(r))throw bl(r);if(rl(r))throw r;return ol(r)&&r.body?await ul(r.body):r}function El(e){if(ll(e))return e.data;if(wr(e)){let t=e.headers.get("Content-Type");return t&&/\bapplication\/json\b/.test(t)?e.json():e.text()}return e}function bl(e){let t=parseInt(e.headers.get("X-Remix-Status"),10)||302,r=e.headers.get("X-Remix-Redirect"),n={},a=e.headers.get("X-Remix-Revalidate");a&&(n["X-Remix-Revalidate"]=a);let i=e.headers.get("X-Remix-Reload-Document");i&&(n["X-Remix-Reload-Document"]=i);let o=e.headers.get("X-Remix-Replace");return o&&(n["X-Remix-Replace"]=o),wn(r,{status:t,headers:n})}function qn(e){if(e.default==null)return;if(!(typeof e.default=="object"&&Object.keys(e.default).length===0))return e.default}function Sl(e,t,r){return r&&e.id!=="root"||t.clientLoader!=null&&(t.clientLoader.hydrate===!0||e.hasLoader!==!0)}/**
 * @remix-run/react v2.16.8
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Ot=new Set,Rl=1e3,Ut=new Set,xl=7680;function Sr(e,t){return e.v3_lazyRouteDiscovery===!0&&!t}function Ll(e,t){let r=new Set(t.state.matches.map(o=>o.route.id)),n=t.state.location.pathname.split("/").filter(Boolean),a=["/"];for(n.pop();n.length>0;)a.push(`/${n.join("/")}`),n.pop();a.forEach(o=>{let s=Ee(t.routes,o,t.basename);s&&s.forEach(l=>r.add(l.route.id))});let i=[...r].reduce((o,s)=>Object.assign(o,{[s]:e.routes[s]}),{});return{...e,routes:i}}function Gl(e,t,r,n,a){if(Sr(r,n))return async({path:i,patch:o,signal:s,fetcherKey:l})=>{Ut.has(i)||await ea([i],l?window.location.href:i,e,t,r,n,a,o,s)}}function Ql(e,t,r,n,a){d.useEffect(()=>{var i;if(!Sr(n,a)||((i=navigator.connection)===null||i===void 0?void 0:i.saveData)===!0)return;function o(c){let m=c.tagName==="FORM"?c.getAttribute("action"):c.getAttribute("href");if(!m)return;let g=new URL(m,window.location.origin);Ut.has(g.pathname)||Ot.add(g.pathname)}async function s(){let c=Array.from(Ot.keys()).filter(m=>Ut.has(m)?(Ot.delete(m),!1):!0);if(c.length!==0)try{await ea(c,null,t,r,n,a,e.basename,e.patchRoutes)}catch(m){console.error("Failed to fetch manifest patches",m)}}document.body.querySelectorAll("a[data-discover], form[data-discover]").forEach(c=>o(c)),s();let l=Dl(s,100);function u(c){return c.nodeType===Node.ELEMENT_NODE}let h=new MutationObserver(c=>{let m=new Set;c.forEach(g=>{[g.target,...g.addedNodes].forEach(v=>{u(v)&&((v.tagName==="A"&&v.getAttribute("data-discover")||v.tagName==="FORM"&&v.getAttribute("data-discover"))&&m.add(v),v.tagName!=="A"&&v.querySelectorAll("a[data-discover], form[data-discover]").forEach(E=>m.add(E)))})}),m.forEach(g=>o(g)),l()});return h.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>h.disconnect()},[n,a,t,r,e])}const rr="remix-manifest-version";async function ea(e,t,r,n,a,i,o,s,l){let u=`${o??"/"}/__manifest`.replace(/\/+/g,"/"),h=new URL(u,window.location.origin);if(e.sort().forEach(E=>h.searchParams.append("p",E)),h.searchParams.set("version",r.version),h.toString().length>xl){Ot.clear();return}let c;try{let E=await fetch(h,{signal:l});if(E.ok){if(E.status===204&&E.headers.has("X-Remix-Reload-Document")){if(!t){console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");return}if(sessionStorage.getItem(rr)===r.version){console.error("Unable to discover routes due to manifest version mismatch.");return}throw sessionStorage.setItem(rr,r.version),window.location.href=t,new Error("Detected manifest version mismatch, reloading...")}else if(E.status>=400)throw new Error(await E.text())}else throw new Error(`${E.status} ${E.statusText}`);sessionStorage.removeItem(rr),c=await E.json()}catch(E){if(l!=null&&l.aborted)return;throw E}let m=new Set(Object.keys(r.routes)),g=Object.values(c).reduce((E,b)=>m.has(b.id)?E:Object.assign(E,{[b.id]:b}),{});Object.assign(r.routes,g),e.forEach(E=>Pl(E,Ut));let v=new Set;Object.values(g).forEach(E=>{(!E.parentId||!g[E.parentId])&&v.add(E.parentId)}),v.forEach(E=>s(E||null,br(g,n,null,a,i,E)))}function Pl(e,t){if(t.size>=Rl){let r=t.values().next().value;typeof r=="string"&&t.delete(r)}t.add(e)}function Dl(e,t){let r;return(...n)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...n),t)}}function ta(){let e=d.useContext(Ye);return be(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Wt(){let e=d.useContext(st);return be(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}const ra=d.createContext(void 0);ra.displayName="Remix";function Xe(){let e=d.useContext(ra);return be(e,"You must render this element inside a <Remix> element"),e}function na(e,t){let[r,n]=d.useState(!1),[a,i]=d.useState(!1),{onFocus:o,onBlur:s,onMouseEnter:l,onMouseLeave:u,onTouchStart:h}=t,c=d.useRef(null);d.useEffect(()=>{if(e==="render"&&i(!0),e==="viewport"){let v=b=>{b.forEach(D=>{i(D.isIntersecting)})},E=new IntersectionObserver(v,{threshold:.5});return c.current&&E.observe(c.current),()=>{E.disconnect()}}},[e]);let m=()=>{e==="intent"&&n(!0)},g=()=>{e==="intent"&&(n(!1),i(!1))};return d.useEffect(()=>{if(r){let v=setTimeout(()=>{i(!0)},100);return()=>{clearTimeout(v)}}},[r]),[a,c,{onFocus:it(o,m),onBlur:it(s,g),onMouseEnter:it(l,m),onMouseLeave:it(u,g),onTouchStart:it(h,m)}]}const Rr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i;function xr(e,t,r){return e==="render"&&!t&&!r?"true":void 0}let Cl=d.forwardRef(({to:e,prefetch:t="none",discover:r="render",...n},a)=>{let i=typeof e=="string"&&Rr.test(e),o=pr(e),[s,l,u]=na(t,n);return d.createElement(d.Fragment,null,d.createElement(yo,q({},n,u,{ref:aa(a,l),to:e,"data-discover":xr(r,i,n.reloadDocument)})),s&&!i?d.createElement(Pr,{page:o}):null)});Cl.displayName="NavLink";let _l=d.forwardRef(({to:e,prefetch:t="none",discover:r="render",...n},a)=>{let i=typeof e=="string"&&Rr.test(e),o=pr(e),[s,l,u]=na(t,n);return d.createElement(d.Fragment,null,d.createElement(An,q({},n,u,{ref:aa(a,l),to:e,"data-discover":xr(r,i,n.reloadDocument)})),s&&!i?d.createElement(Pr,{page:o}):null)});_l.displayName="Link";let Tl=d.forwardRef(({discover:e="render",...t},r)=>{let n=typeof t.action=="string"&&Rr.test(t.action);return d.createElement(Nn,q({},t,{ref:r,"data-discover":xr(e,n,t.reloadDocument)}))});Tl.displayName="Form";function it(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function Lr(e,t,r){if(r&&!Mt)return[e[0]];if(t){let n=e.findIndex(a=>t[a.route.id]!==void 0);return e.slice(0,n+1)}return e}function Zl(){let{isSpaMode:e,manifest:t,routeModules:r,criticalCss:n}=Xe(),{errors:a,matches:i}=Wt(),o=Lr(i,a,e),s=d.useMemo(()=>Jo(o,r,t),[o,r,t]);return d.createElement(d.Fragment,null,n?d.createElement("style",{dangerouslySetInnerHTML:{__html:n}}):null,s.map(({key:l,link:u})=>yr(u)?d.createElement(Pr,q({key:l},u)):d.createElement("link",q({key:l},u))))}function Pr({page:e,...t}){let{router:r}=ta(),n=d.useMemo(()=>Ee(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?d.createElement(Ml,q({page:e,matches:n},t)):(console.warn(`Tried to prefetch ${e} but no routes matched.`),null)}function Ol(e){let{manifest:t,routeModules:r}=Xe(),[n,a]=d.useState([]);return d.useEffect(()=>{let i=!1;return Yo(e,t,r).then(o=>{i||a(o)}),()=>{i=!0}},[e,t,r]),n}function Ml({page:e,matches:t,...r}){let n=Re(),{future:a,manifest:i,routeModules:o}=Xe(),{loaderData:s,matches:l}=Wt(),u=d.useMemo(()=>sn(e,t,l,i,n,a,"data"),[e,t,l,i,n,a]),h=d.useMemo(()=>{if(!a.v3_singleFetch)return Xo(e,u,i);if(e===n.pathname+n.search+n.hash)return[];let v=new Set,E=!1;if(t.forEach(D=>{var R;i.routes[D.route.id].hasLoader&&(!u.some(P=>P.route.id===D.route.id)&&D.route.id in s&&(R=o[D.route.id])!==null&&R!==void 0&&R.shouldRevalidate||i.routes[D.route.id].hasClientLoader?E=!0:v.add(D.route.id))}),v.size===0)return[];let b=zt(e);return E&&v.size>0&&b.searchParams.set("_routes",t.filter(D=>v.has(D.route.id)).map(D=>D.route.id).join(",")),[b.pathname+b.search]},[a.v3_singleFetch,s,n,i,u,t,e,o]),c=d.useMemo(()=>sn(e,t,l,i,n,a,"assets"),[e,t,l,i,n,a]),m=d.useMemo(()=>Go(c,i),[c,i]),g=Ol(c);return d.createElement(d.Fragment,null,h.map(v=>d.createElement("link",q({key:v,rel:"prefetch",as:"fetch",href:v},r))),m.map(v=>d.createElement("link",q({key:v,rel:"modulepreload",href:v},r))),g.map(({key:v,link:E})=>d.createElement("link",q({key:v},E))))}function ql(){let{isSpaMode:e,routeModules:t}=Xe(),{errors:r,matches:n,loaderData:a}=Wt(),i=Re(),o=Lr(n,r,e),s=null;r&&(s=r[o[o.length-1].route.id]);let l=[],u=null,h=[];for(let c=0;c<o.length;c++){let m=o[c],g=m.route.id,v=a[g],E=m.params,b=t[g],D=[],R={id:g,data:v,meta:[],params:m.params,pathname:m.pathname,handle:m.route.handle,error:s};if(h[c]=R,b!=null&&b.meta?D=typeof b.meta=="function"?b.meta({data:v,params:E,location:i,matches:h,error:s}):Array.isArray(b.meta)?[...b.meta]:b.meta:u&&(D=[...u]),D=D||[],!Array.isArray(D))throw new Error("The route at "+m.route.path+` returns an invalid value. All route meta functions must return an array of meta objects.

To reference the meta function API, see https://remix.run/route/meta`);R.meta=D,h[c]=R,l=[...D],u=l}return d.createElement(d.Fragment,null,l.flat().map(c=>{if(!c)return null;if("tagName"in c){let{tagName:m,...g}=c;if(!Fl(m))return console.warn(`A meta object uses an invalid tagName: ${m}. Expected either 'link' or 'meta'`),null;let v=m;return d.createElement(v,q({key:JSON.stringify(g)},g))}if("title"in c)return d.createElement("title",{key:"title"},String(c.title));if("charset"in c&&(c.charSet??(c.charSet=c.charset),delete c.charset),"charSet"in c&&c.charSet!=null)return typeof c.charSet=="string"?d.createElement("meta",{key:"charSet",charSet:c.charSet}):null;if("script:ld+json"in c)try{let m=JSON.stringify(c["script:ld+json"]);return d.createElement("script",{key:`script:ld+json:${m}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:m}})}catch{return null}return d.createElement("meta",q({key:JSON.stringify(c)},c))}))}function Fl(e){return typeof e=="string"&&/^(meta|link)$/.test(e)}function Al(e){return d.createElement(Ji,e)}let Mt=!1;function Nl(e){let{manifest:t,serverHandoffString:r,abortDelay:n,serializeError:a,isSpaMode:i,future:o,renderMeta:s}=Xe(),{router:l,static:u,staticContext:h}=ta(),{matches:c}=Wt(),m=Sr(o,i);s&&(s.didRenderScripts=!0);let g=Lr(c,null,i);d.useEffect(()=>{Mt=!0},[]);let v=(_,y)=>{let C;return a&&y instanceof Error?C=a(y):C=y,`${JSON.stringify(_)}:__remixContext.p(!1, ${Dt(JSON.stringify(C))})`},E=(_,y,C)=>{let $;try{$=JSON.stringify(C)}catch(I){return v(y,I)}return`${JSON.stringify(y)}:__remixContext.p(${Dt($)})`},b=(_,y,C)=>{let $;return a&&C instanceof Error?$=a(C):$=C,`__remixContext.r(${JSON.stringify(_)}, ${JSON.stringify(y)}, !1, ${Dt(JSON.stringify($))})`},D=(_,y,C)=>{let $;try{$=JSON.stringify(C)}catch(I){return b(_,y,I)}return`__remixContext.r(${JSON.stringify(_)}, ${JSON.stringify(y)}, ${Dt($)})`},R=[],P=d.useMemo(()=>{var _;let y=o.v3_singleFetch?"window.__remixContext.stream = new ReadableStream({start(controller){window.__remixContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());":"",C=h?`window.__remixContext = ${r};${y}`:" ",$=o.v3_singleFetch||h==null?void 0:h.activeDeferreds;C+=$?["__remixContext.p = function(v,e,p,x) {","  if (typeof e !== 'undefined') {",`    x=new Error("Unexpected Server Error");
    x.stack=undefined;`,"    p=Promise.reject(x);","  } else {","    p=Promise.resolve(v);","  }","  return p;","};","__remixContext.n = function(i,k) {","  __remixContext.t = __remixContext.t || {};","  __remixContext.t[i] = __remixContext.t[i] || {};","  let p = new Promise((r, e) => {__remixContext.t[i][k] = {r:(v)=>{r(v);},e:(v)=>{e(v);}};});",typeof n=="number"?`setTimeout(() => {if(typeof p._error !== "undefined" || typeof p._data !== "undefined"){return;} __remixContext.t[i][k].e(new Error("Server timeout."))}, ${n});`:"","  return p;","};","__remixContext.r = function(i,k,v,e,p,x) {","  p = __remixContext.t[i][k];","  if (typeof e !== 'undefined') {",`    x=new Error("Unexpected Server Error");
    x.stack=undefined;`,"    p.e(x);","  } else {","    p.r(v);","  }","};"].join(`
`)+Object.entries($).map(([K,W])=>{let ae=new Set(W.pendingKeys),ne=W.deferredKeys.map(ee=>{if(ae.has(ee))return R.push(d.createElement(hn,{key:`${K} | ${ee}`,deferredData:W,routeId:K,dataKey:ee,scriptProps:e,serializeData:D,serializeError:b})),`${JSON.stringify(ee)}:__remixContext.n(${JSON.stringify(K)}, ${JSON.stringify(ee)})`;{let xe=W.data[ee];return typeof xe._error<"u"?v(ee,xe._error):E(K,ee,xe._data)}}).join(`,
`);return`Object.assign(__remixContext.state.loaderData[${JSON.stringify(K)}], {${ne}});`}).join(`
`)+(R.length>0?`__remixContext.a=${R.length};`:""):"";let I=u?`${(_=t.hmr)!==null&&_!==void 0&&_.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${m?"":`import ${JSON.stringify(t.url)}`};
${g.map((K,W)=>`import * as route${W} from ${JSON.stringify(t.routes[K.route.id].module)};`).join(`
`)}
${m?`window.__remixManifest = ${JSON.stringify(Ll(t,l),null,2)};`:""}
window.__remixRouteModules = {${g.map((K,W)=>`${JSON.stringify(K.route.id)}:route${W}`).join(",")}};

import(${JSON.stringify(t.entry.module)});`:" ";return d.createElement(d.Fragment,null,d.createElement("script",q({},e,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:un(C),type:void 0})),d.createElement("script",q({},e,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:un(I),type:"module",async:!0})))},[]);if(!u&&typeof __remixContext=="object"&&__remixContext.a)for(let _=0;_<__remixContext.a;_++)R.push(d.createElement(hn,{key:_,scriptProps:e,serializeData:D,serializeError:b}));let x=g.map(_=>{let y=t.routes[_.route.id];return(y.imports||[]).concat([y.module])}).flat(1),A=Mt?[]:t.entry.imports.concat(x);return Mt?null:d.createElement(d.Fragment,null,m?null:d.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin}),d.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin}),kl(A).map(_=>d.createElement("link",{key:_,rel:"modulepreload",href:_,crossOrigin:e.crossOrigin})),P,R)}function hn({dataKey:e,deferredData:t,routeId:r,scriptProps:n,serializeData:a,serializeError:i}){return typeof document>"u"&&t&&e&&r&&be(t.pendingKeys.includes(e),`Deferred data for route ${r} with key ${e} was not pending but tried to render a script for it.`),d.createElement(d.Suspense,{fallback:typeof document>"u"&&t&&e&&r?null:d.createElement("script",q({},n,{async:!0,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:" "}}))},typeof document>"u"&&t&&e&&r?d.createElement(Al,{resolve:t.data[e],errorElement:d.createElement(jl,{dataKey:e,routeId:r,scriptProps:n,serializeError:i}),children:o=>d.createElement("script",q({},n,{async:!0,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:a(r,e,o)}}))}):d.createElement("script",q({},n,{async:!0,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:" "}})))}function jl({dataKey:e,routeId:t,scriptProps:r,serializeError:n}){let a=$i();return d.createElement("script",q({},r,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:n(t,e,a)}}))}function kl(e){return[...new Set(e)]}function es(){return ki()}function ts(){return Ii()}function rs(e={}){return So(e)}function aa(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}export{ke as E,Tl as F,Zl as L,ql as M,Bl as O,ra as R,Nl as S,q as _,ts as a,Xe as b,Re as c,ji as d,Kl as e,rs as f,ml as g,br as h,be as i,Hl as j,Gl as k,Vl as l,Ee as m,$l as n,Xl as o,Ql as p,Yl as q,Jl as r,Sl as s,zl as t,es as u,_l as v,On as w};
