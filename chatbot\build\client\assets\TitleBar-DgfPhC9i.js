import{r as x,R as t}from"./index-C0U6NBub.js";import{c,v as u,T as y,a as A,U as T}from"./Page-BfvU96Zb.js";const g=x.createContext(!1);var i={Layout:"Polaris-Layout",Section:"Polaris-Layout__Section","Section-fullWidth":"Polaris-Layout__Section--fullWidth","Section-oneHalf":"Polaris-Layout__Section--oneHalf","Section-oneThird":"Polaris-Layout__Section--oneThird",AnnotatedSection:"Polaris-Layout__AnnotatedSection",AnnotationWrapper:"Polaris-Layout__AnnotationWrapper",AnnotationContent:"Polaris-Layout__AnnotationContent",Annotation:"Polaris-Layout__Annotation"},E={TextContainer:"Polaris-TextContainer",spacingTight:"Polaris-TextContainer--spacingTight",spacingLoose:"Polaris-TextContainer--spacingLoose"};function C({spacing:n,children:a}){const e=c(E.TextContainer,n&&E[u("spacing",n)]);return t.createElement("div",{className:e},a)}function h({children:n,title:a,description:e,id:o}){const s=typeof e=="string"?t.createElement(y,{as:"p",variant:"bodyMd"},e):e;return t.createElement("div",{className:i.AnnotatedSection},t.createElement("div",{className:i.AnnotationWrapper},t.createElement("div",{className:i.Annotation},t.createElement(C,{spacing:"tight"},t.createElement(y,{id:o,variant:"headingMd",as:"h2"},a),s&&t.createElement(A,{color:"text-secondary"},s))),t.createElement("div",{className:i.AnnotationContent},n)))}function N({children:n,variant:a}){const e=c(i.Section,i[`Section-${a}`]);return t.createElement("div",{className:e},n)}const v=function({sectioned:a,children:e}){const o=a?t.createElement(N,null,e):e;return t.createElement("div",{className:i.Layout},o)};v.AnnotatedSection=h;v.Section=N;var m={Link:"Polaris-Link",monochrome:"Polaris-Link--monochrome",removeUnderline:"Polaris-Link--removeUnderline"};function W({url:n,children:a,onClick:e,external:o,target:s,id:r,monochrome:P,removeUnderline:_,accessibilityLabel:L,dataPrimaryLink:d}){return t.createElement(g.Consumer,null,S=>{const f=P||S,p=c(m.Link,f&&m.monochrome,_&&m.removeUnderline);return n?t.createElement(T,{onClick:e,className:p,url:n,external:o,target:s,id:r,"aria-label":L,"data-primary-link":d},a):t.createElement("button",{type:"button",onClick:e,className:p,id:r,"aria-label":L,"data-primary-link":d},a)})}var l={List:"Polaris-List",typeNumber:"Polaris-List--typeNumber",Item:"Polaris-List__Item",spacingLoose:"Polaris-List--spacingLoose"};function k({children:n}){return t.createElement("li",{className:l.Item},n)}const b=function({children:a,gap:e="loose",type:o="bullet"}){const s=c(l.List,e&&l[u("spacing",e)],o&&l[u("type",o)]),r=o==="bullet"?"ul":"ol";return t.createElement(r,{className:s},a)};b.Item=k;const B="ui-title-bar";export{v as L,B as T,W as a,b};
