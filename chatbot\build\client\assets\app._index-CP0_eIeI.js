import{r as b,j as e}from"./index-C0U6NBub.js";import{f as v}from"./components-DH2eW-1_.js";import{P as y,B as i,C as o,T as r,I as n,b as m,a as j}from"./Page-BfvU96Zb.js";import{T as w,L as l,a,b as p}from"./TitleBar-DgfPhC9i.js";import"./context-JLkqRqjJ.js";const k=new Proxy({},{get(t,s){throw Error(`shopify.${String(s)} can't be used in a server environment. You likely need to move this code into an Effect.`)}});function P(){if(typeof window>"u")return k;if(!window.shopify)throw Error("The shopify global is not defined. This likely means the App Bridge script tag was not added correctly to this page");return window.shopify}function I(){var h,u,x,g;const t=v(),s=P(),f=["loading","submitting"].includes(t.state)&&t.formMethod==="POST",d=(u=(h=t.data)==null?void 0:h.product)==null?void 0:u.id.replace("gid://shopify/Product/","");b.useEffect(()=>{d&&s.toast.show("Product created")},[d,s]);const c=()=>t.submit({},{method:"POST"});return e.jsxs(y,{children:[e.jsx(w,{title:"Remix app template",children:e.jsx("button",{variant:"primary",onClick:c,children:"Generate a product"})}),e.jsx(i,{gap:"500",children:e.jsxs(l,{children:[e.jsx(l.Section,{children:e.jsx(o,{children:e.jsxs(i,{gap:"500",children:[e.jsxs(i,{gap:"200",children:[e.jsx(r,{as:"h2",variant:"headingMd",children:"Congrats on creating a new Shopify app 🎉"}),e.jsxs(r,{variant:"bodyMd",as:"p",children:["This embedded app template uses"," ",e.jsx(a,{url:"https://shopify.dev/docs/apps/tools/app-bridge",target:"_blank",removeUnderline:!0,children:"App Bridge"})," ","interface examples like an"," ",e.jsx(a,{url:"/app/additional",removeUnderline:!0,children:"additional page in the app nav"}),", as well as an"," ",e.jsx(a,{url:"https://shopify.dev/docs/api/admin-graphql",target:"_blank",removeUnderline:!0,children:"Admin GraphQL"})," ","mutation demo, to provide a starting point for app development."]})]}),e.jsxs(i,{gap:"200",children:[e.jsx(r,{as:"h3",variant:"headingMd",children:"Get started with products"}),e.jsxs(r,{as:"p",variant:"bodyMd",children:["Generate a product with GraphQL and get the JSON output for that product. Learn more about the"," ",e.jsx(a,{url:"https://shopify.dev/docs/api/admin-graphql/latest/mutations/productCreate",target:"_blank",removeUnderline:!0,children:"productCreate"})," ","mutation in our API references."]})]}),e.jsxs(n,{gap:"300",children:[e.jsx(m,{loading:f,onClick:c,children:"Generate a product"}),((x=t.data)==null?void 0:x.product)&&e.jsx(m,{url:`shopify:admin/products/${d}`,target:"_blank",variant:"plain",children:"View product"})]}),((g=t.data)==null?void 0:g.product)&&e.jsxs(e.Fragment,{children:[e.jsxs(r,{as:"h3",variant:"headingMd",children:[" ","productCreate mutation"]}),e.jsx(j,{padding:"400",background:"bg-surface-active",borderWidth:"025",borderRadius:"200",borderColor:"border",overflowX:"scroll",children:e.jsx("pre",{style:{margin:0},children:e.jsx("code",{children:JSON.stringify(t.data.product,null,2)})})}),e.jsxs(r,{as:"h3",variant:"headingMd",children:[" ","productVariantsBulkUpdate mutation"]}),e.jsx(j,{padding:"400",background:"bg-surface-active",borderWidth:"025",borderRadius:"200",borderColor:"border",overflowX:"scroll",children:e.jsx("pre",{style:{margin:0},children:e.jsx("code",{children:JSON.stringify(t.data.variant,null,2)})})})]})]})})}),e.jsx(l.Section,{variant:"oneThird",children:e.jsxs(i,{gap:"500",children:[e.jsx(o,{children:e.jsxs(i,{gap:"200",children:[e.jsx(r,{as:"h2",variant:"headingMd",children:"App template specs"}),e.jsxs(i,{gap:"200",children:[e.jsxs(n,{align:"space-between",children:[e.jsx(r,{as:"span",variant:"bodyMd",children:"Framework"}),e.jsx(a,{url:"https://remix.run",target:"_blank",removeUnderline:!0,children:"Remix"})]}),e.jsxs(n,{align:"space-between",children:[e.jsx(r,{as:"span",variant:"bodyMd",children:"Database"}),e.jsx(a,{url:"https://www.prisma.io/",target:"_blank",removeUnderline:!0,children:"Prisma"})]}),e.jsxs(n,{align:"space-between",children:[e.jsx(r,{as:"span",variant:"bodyMd",children:"Interface"}),e.jsxs("span",{children:[e.jsx(a,{url:"https://polaris.shopify.com",target:"_blank",removeUnderline:!0,children:"Polaris"}),", ",e.jsx(a,{url:"https://shopify.dev/docs/apps/tools/app-bridge",target:"_blank",removeUnderline:!0,children:"App Bridge"})]})]}),e.jsxs(n,{align:"space-between",children:[e.jsx(r,{as:"span",variant:"bodyMd",children:"API"}),e.jsx(a,{url:"https://shopify.dev/docs/api/admin-graphql",target:"_blank",removeUnderline:!0,children:"GraphQL API"})]})]})]})}),e.jsx(o,{children:e.jsxs(i,{gap:"200",children:[e.jsx(r,{as:"h2",variant:"headingMd",children:"Next steps"}),e.jsxs(p,{children:[e.jsxs(p.Item,{children:["Build an"," ",e.jsxs(a,{url:"https://shopify.dev/docs/apps/getting-started/build-app-example",target:"_blank",removeUnderline:!0,children:[" ","example app"]})," ","to get started"]}),e.jsxs(p.Item,{children:["Explore Shopify’s API with"," ",e.jsx(a,{url:"https://shopify.dev/docs/apps/tools/graphiql-admin-api",target:"_blank",removeUnderline:!0,children:"GraphiQL"})]})]})]})})]})})]})})]})}export{I as default};
