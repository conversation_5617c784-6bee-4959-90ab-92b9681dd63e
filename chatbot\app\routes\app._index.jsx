import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useLoaderD<PERSON> } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Box,
  List,
  Link,
  InlineStack,
  <PERSON><PERSON>,
  <PERSON>,
  Spinner,
} from "@shopify/polaris";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { getScriptTags, getChatbotScriptUrl, ensureScriptTag, removeScriptTagsBySrc } from "../utils/scriptTags.server";

export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);

  try {
    // Get current script tags
    const scriptTags = await getScriptTags(session);
    const appUrl = process.env.SHOPIFY_APP_URL;
    const chatbotScriptUrl = appUrl ? getChatbotScriptUrl(appUrl) : null;

    // Find our chatbot script tag
    const chatbotScriptTag = scriptTags.find(tag =>
      chatbotScriptUrl && tag.src === chatbotScriptUrl
    );

    return Response.json({
      shop: session.shop,
      scriptTags: scriptTags,
      chatbotScriptTag: chatbotScriptTag,
      chatbotScriptUrl: chatbotScriptUrl,
      appUrl: appUrl,
      isInstalled: !!chatbotScriptTag
    });
  } catch (error) {
    console.error("Error loading dashboard data:", error);
    return Response.json({
      shop: session.shop,
      scriptTags: [],
      chatbotScriptTag: null,
      chatbotScriptUrl: null,
      appUrl: process.env.SHOPIFY_APP_URL,
      isInstalled: false,
      error: error.message
    });
  }
};

export const action = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  try {
    const appUrl = process.env.SHOPIFY_APP_URL;
    if (!appUrl) {
      return Response.json({ error: "App URL not configured" }, { status: 500 });
    }

    const scriptUrl = getChatbotScriptUrl(appUrl);

    switch (action) {
      case "install_script":
        const scriptTag = await ensureScriptTag(session, scriptUrl, "ONLINE_STORE");
        return Response.json({
          success: true,
          message: "Chatbot script tag installed successfully",
          scriptTag
        });

      case "uninstall_script":
        const removedCount = await removeScriptTagsBySrc(session, scriptUrl);
        return Response.json({
          success: true,
          message: `Removed ${removedCount} chatbot script tag(s)`,
          removedCount
        });

      default:
        return Response.json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error in dashboard action:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
};

export default function ChatbotDashboard() {
  const data = useLoaderData();
  const fetcher = useFetcher();
  const shopify = useAppBridge();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const isLoading = ["loading", "submitting"].includes(fetcher.state);

  useEffect(() => {
    if (fetcher.data?.success) {
      shopify.toast.show(fetcher.data.message);
      // Refresh the page data
      setIsRefreshing(true);
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else if (fetcher.data?.error) {
      shopify.toast.show(fetcher.data.error, { isError: true });
    }
  }, [fetcher.data, shopify]);

  const installScript = () => {
    fetcher.submit({ action: "install_script" }, { method: "POST" });
  };

  const uninstallScript = () => {
    fetcher.submit({ action: "uninstall_script" }, { method: "POST" });
  };

  return (
    <Page>
      <TitleBar title="Chatbot Dashboard" />

      {data.error && (
        <Banner status="critical" title="Error loading dashboard">
          <p>{data.error}</p>
        </Banner>
      )}

      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    🤖 Chatbot Status for {data.shop}
                  </Text>
                  <InlineStack gap="200" align="start">
                    <Badge
                      status={data.isInstalled ? "success" : "attention"}
                      size="medium"
                    >
                      {data.isInstalled ? "Active" : "Not Installed"}
                    </Badge>
                    {isRefreshing && <Spinner size="small" />}
                  </InlineStack>
                </BlockStack>

                {data.isInstalled ? (
                  <BlockStack gap="300">
                    <Text variant="bodyMd" as="p" color="success">
                      ✅ Your chatbot is successfully installed and running on your storefront!
                    </Text>
                    <Box
                      padding="400"
                      background="bg-surface-success"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border-success"
                    >
                      <BlockStack gap="200">
                        <Text as="h4" variant="headingSm">Script Tag Details:</Text>
                        <Text variant="bodySm">
                          <strong>Script URL:</strong> {data.chatbotScriptTag?.src}
                        </Text>
                        <Text variant="bodySm">
                          <strong>Display Scope:</strong> {data.chatbotScriptTag?.display_scope || 'online_store'}
                        </Text>
                        <Text variant="bodySm">
                          <strong>Created:</strong> {data.chatbotScriptTag?.created_at ? new Date(data.chatbotScriptTag.created_at).toLocaleString() : 'Unknown'}
                        </Text>
                      </BlockStack>
                    </Box>
                    <InlineStack gap="300">
                      <Button
                        variant="primary"
                        tone="critical"
                        loading={isLoading}
                        onClick={uninstallScript}
                      >
                        Uninstall Chatbot
                      </Button>
                      <Button
                        url={`https://${data.shop}`}
                        target="_blank"
                        variant="plain"
                      >
                        View Storefront
                      </Button>
                    </InlineStack>
                  </BlockStack>
                ) : (
                  <BlockStack gap="300">
                    <Text variant="bodyMd" as="p">
                      Your chatbot is not currently installed on your storefront. Click the button below to install it.
                    </Text>
                    <Button
                      variant="primary"
                      loading={isLoading}
                      onClick={installScript}
                    >
                      Install Chatbot
                    </Button>
                  </BlockStack>
                )}
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    📊 Script Tags Overview
                  </Text>
                  <Text variant="bodyMd">
                    Total script tags: <strong>{data.scriptTags?.length || 0}</strong>
                  </Text>
                  {data.scriptTags?.length > 0 && (
                    <Box
                      padding="300"
                      background="bg-surface-secondary"
                      borderWidth="025"
                      borderRadius="200"
                      borderColor="border"
                    >
                      <BlockStack gap="100">
                        {data.scriptTags.slice(0, 5).map((tag, index) => (
                          <Text key={index} variant="bodySm">
                            • {tag.src?.split('/').pop() || 'Unknown script'}
                          </Text>
                        ))}
                        {data.scriptTags.length > 5 && (
                          <Text variant="bodySm" color="subdued">
                            ... and {data.scriptTags.length - 5} more
                          </Text>
                        )}
                      </BlockStack>
                    </Box>
                  )}
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    🔧 Configuration
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        App URL
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        {data.appUrl ? new URL(data.appUrl).hostname : 'Not configured'}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Script URL
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        {data.chatbotScriptUrl ? '/chatbot-loader.js' : 'Not available'}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Embed Script
                      </Text>
                      <Text variant="bodySm" color="subdued">
                        /chatbot-embed.js
                      </Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    📚 Features
                  </Text>
                  <List>
                    <List.Item>
                      Automatic customer identification
                    </List.Item>
                    <List.Item>
                      Real-time chat interface
                    </List.Item>
                    <List.Item>
                      Mobile-responsive design
                    </List.Item>
                    <List.Item>
                      Easy installation & removal
                    </List.Item>
                  </List>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}