# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "f1bad3947b5cfa35f788432f1cf25e7e"
name = "chatbot"
handle = "chatbot-250"
application_url = "https://class-silicon-scenic-qualified.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"

  [[webhooks.subscriptions]]
  topics = [ "app/installed" ]
  uri = "/webhooks/app/installed"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products,write_script_tags,read_script_tags"

[auth]
redirect_urls = [
  "https://class-silicon-scenic-qualified.trycloudflare.com/auth/callback",
  "https://class-silicon-scenic-qualified.trycloudflare.com/auth/shopify/callback",
  "https://class-silicon-scenic-qualified.trycloudflare.com/api/auth/callback"
]

[pos]
embedded = false
