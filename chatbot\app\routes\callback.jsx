import { redirect } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { ensureScriptTag, getChatbotScriptUrl } from "../utils/scriptTags.server";

export async function loader({ request }) {
  try {
    const { session } = await authenticate.admin(request);

    if (!session) {
      throw new Response("No session found", { status: 400 });
    }

    // Get the app URL from environment variables
    const appUrl = process.env.SHOPIFY_APP_URL;
    if (appUrl) {
      try {
        // Create the script tag URL
        const scriptUrl = getChatbotScriptUrl(appUrl);

        // Ensure the chatbot script tag is installed
        await ensureScriptTag(session, scriptUrl, "online_store");

        console.log(`Script tag ensured for ${session.shop} during callback`);
      } catch (scriptError) {
        console.error(`Failed to ensure script tag during callback for ${session.shop}:`, scriptError);
        // Don't fail the callback, just log the error
      }
    }

    return redirect(`/install?shop=${session.shop}`);

  } catch (error) {
    console.error("Error in callback:", error);
    return redirect("/auth/login");
  }
}