import { redirect } from "@remix-run/node";
import { Shopify, registerScript } from "~/utils/shopify";

export async function loader({ request }) {
  const url = new URL(request.url);
  const session = await Shopify.Auth.validateAuthCallback(request, url);

  const shop = session.shop;
  const accessToken = session.accessToken;

  await registerScript(shop, accessToken);

  return redirect(`/install?shop=${shop}`);
}